// lib/services/template_service.dart
import 'dart:convert';
import '../models/email_template_model.dart';
import '../models/recipient_model.dart';
import '../database/repositories/email_templates_repository.dart';

class TemplateService {
  static final TemplateService _instance = TemplateService._internal();
  factory TemplateService() => _instance;
  TemplateService._internal();

  final EmailTemplatesRepository _repository = EmailTemplatesRepository();

  // Create new template
  Future<String> createTemplate(EmailTemplate template) async {
    try {
      // Extract variables from content
      final extractedTemplate = template.copyWith(
        variables: _extractVariables(template.htmlContent, template.subject),
      );
      
      return await _repository.create(extractedTemplate);
    } catch (e) {
      throw TemplateException('Failed to create template: ${e.toString()}');
    }
  }

  // Get template by ID
  Future<EmailTemplate?> getTemplate(String id) async {
    try {
      return await _repository.getById(id);
    } catch (e) {
      throw TemplateException('Failed to get template: ${e.toString()}');
    }
  }

  // Get all templates with filtering
  Future<List<EmailTemplate>> getTemplates({
    int? limit,
    int? offset,
    String? searchQuery,
    String? category,
    bool? isActive,
  }) async {
    try {
      return await _repository.getAll(
        limit: limit,
        offset: offset,
        searchQuery: searchQuery,
        category: category,
        isActive: isActive,
      );
    } catch (e) {
      throw TemplateException('Failed to get templates: ${e.toString()}');
    }
  }

  // Get template count
  Future<int> getTemplateCount({
    String? searchQuery,
    String? category,
    bool? isActive,
  }) async {
    try {
      return await _repository.getCount(
        searchQuery: searchQuery,
        category: category,
        isActive: isActive,
      );
    } catch (e) {
      throw TemplateException('Failed to get template count: ${e.toString()}');
    }
  }

  // Update template
  Future<void> updateTemplate(EmailTemplate template) async {
    try {
      // Re-extract variables when updating
      final updatedTemplate = template.copyWith(
        variables: _extractVariables(template.htmlContent, template.subject),
      );
      
      await _repository.update(updatedTemplate);
    } catch (e) {
      throw TemplateException('Failed to update template: ${e.toString()}');
    }
  }

  // Delete template
  Future<void> deleteTemplate(String id) async {
    try {
      await _repository.delete(id);
    } catch (e) {
      throw TemplateException('Failed to delete template: ${e.toString()}');
    }
  }

  // Duplicate template
  Future<EmailTemplate?> duplicateTemplate(String id, String newName) async {
    try {
      return await _repository.duplicate(id, newName);
    } catch (e) {
      throw TemplateException('Failed to duplicate template: ${e.toString()}');
    }
  }

  // Get categories
  Future<List<String>> getCategories() async {
    try {
      return await _repository.getCategories();
    } catch (e) {
      throw TemplateException('Failed to get categories: ${e.toString()}');
    }
  }

  // Get active templates
  Future<List<EmailTemplate>> getActiveTemplates() async {
    try {
      return await _repository.getActive();
    } catch (e) {
      throw TemplateException('Failed to get active templates: ${e.toString()}');
    }
  }

  // Render template with recipient data
  RenderedTemplate renderTemplate(EmailTemplate template, Recipient recipient, {Map<String, dynamic>? additionalData}) {
    try {
      final data = <String, dynamic>{
        'name': recipient.name,
        'email': recipient.email,
        'company': recipient.company ?? '',
        'phone': recipient.phone ?? '',
        ...recipient.customFields,
        ...?additionalData,
      };

      final renderedSubject = _renderContent(template.subject, data);
      final renderedHtmlContent = _renderContent(template.htmlContent, data);
      final renderedPlainTextContent = template.plainTextContent != null 
          ? _renderContent(template.plainTextContent!, data)
          : null;

      return RenderedTemplate(
        subject: renderedSubject,
        htmlContent: renderedHtmlContent,
        plainTextContent: renderedPlainTextContent,
        recipientEmail: recipient.email,
        recipientName: recipient.name,
      );
    } catch (e) {
      throw TemplateException('Failed to render template: ${e.toString()}');
    }
  }

  // Preview template with sample data
  RenderedTemplate previewTemplate(EmailTemplate template, {Map<String, dynamic>? sampleData}) {
    final defaultSampleData = {
      'name': 'John Doe',
      'email': '<EMAIL>',
      'company': 'Acme Corporation',
      'phone': '+****************',
      'first_name': 'John',
      'last_name': 'Doe',
      'date': DateTime.now().toString().split(' ')[0],
      'time': DateTime.now().toString().split(' ')[1].substring(0, 5),
      ...?sampleData,
    };

    try {
      final renderedSubject = _renderContent(template.subject, defaultSampleData);
      final renderedHtmlContent = _renderContent(template.htmlContent, defaultSampleData);
      final renderedPlainTextContent = template.plainTextContent != null 
          ? _renderContent(template.plainTextContent!, defaultSampleData)
          : null;

      return RenderedTemplate(
        subject: renderedSubject,
        htmlContent: renderedHtmlContent,
        plainTextContent: renderedPlainTextContent,
        recipientEmail: defaultSampleData['email'] as String,
        recipientName: defaultSampleData['name'] as String,
      );
    } catch (e) {
      throw TemplateException('Failed to preview template: ${e.toString()}');
    }
  }

  // Validate template
  TemplateValidationResult validateTemplate(EmailTemplate template) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check required fields
    if (template.name.trim().isEmpty) {
      errors.add('Template name is required');
    }

    if (template.subject.trim().isEmpty) {
      errors.add('Subject is required');
    }

    if (template.htmlContent.trim().isEmpty) {
      errors.add('HTML content is required');
    }

    // Check for unmatched variables
    final subjectVars = _extractVariables(template.subject);
    final contentVars = _extractVariables(template.htmlContent);
    final allVars = {...subjectVars, ...contentVars};

    // Check for common variable naming issues
    for (final variable in allVars) {
      if (variable.contains(' ')) {
        warnings.add('Variable "$variable" contains spaces - consider using underscores');
      }
      if (variable.toUpperCase() == variable && variable.length > 1) {
        warnings.add('Variable "$variable" is all uppercase - consider using lowercase');
      }
    }

    // Check HTML structure
    if (template.type == TemplateType.html || template.type == TemplateType.mixed) {
      if (!template.htmlContent.toLowerCase().contains('<html>') && 
          !template.htmlContent.toLowerCase().contains('<!doctype')) {
        warnings.add('HTML content should include proper HTML structure');
      }
    }

    // Check for potential issues
    if (template.htmlContent.length > 100000) {
      warnings.add('HTML content is very large (${template.htmlContent.length} characters) - consider optimizing');
    }

    if (allVars.length > 20) {
      warnings.add('Template has many variables (${allVars.length}) - consider simplifying');
    }

    return TemplateValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      variableCount: allVars.length,
      variables: allVars.toList(),
    );
  }

  // Get template statistics
  Future<TemplateStats> getTemplateStats() async {
    try {
      final allTemplates = await _repository.getAll();
      final activeTemplates = allTemplates.where((t) => t.isActive).toList();
      final categories = await _repository.getCategories();

      final typeStats = <TemplateType, int>{};
      for (final type in TemplateType.values) {
        typeStats[type] = allTemplates.where((t) => t.type == type).length;
      }

      return TemplateStats(
        totalTemplates: allTemplates.length,
        activeTemplates: activeTemplates.length,
        inactiveTemplates: allTemplates.length - activeTemplates.length,
        categoriesCount: categories.length,
        typeStats: typeStats,
        averageVariableCount: allTemplates.isEmpty 
            ? 0 
            : allTemplates.map((t) => t.variables.length).reduce((a, b) => a + b) / allTemplates.length,
      );
    } catch (e) {
      throw TemplateException('Failed to get template stats: ${e.toString()}');
    }
  }

  // Private helper methods
  List<String> _extractVariables(String content, [String? additionalContent]) {
    final variables = <String>{};
    final regex = RegExp(r'\{\{([^}]+)\}\}');
    
    // Extract from main content
    final matches = regex.allMatches(content);
    for (final match in matches) {
      final variable = match.group(1)?.trim();
      if (variable != null && variable.isNotEmpty) {
        variables.add(variable);
      }
    }

    // Extract from additional content if provided
    if (additionalContent != null) {
      final additionalMatches = regex.allMatches(additionalContent);
      for (final match in additionalMatches) {
        final variable = match.group(1)?.trim();
        if (variable != null && variable.isNotEmpty) {
          variables.add(variable);
        }
      }
    }

    return variables.toList()..sort();
  }

  String _renderContent(String content, Map<String, dynamic> data) {
    String rendered = content;
    
    // Replace variables with actual data
    final regex = RegExp(r'\{\{([^}]+)\}\}');
    rendered = rendered.replaceAllMapped(regex, (match) {
      final variable = match.group(1)?.trim();
      if (variable != null && data.containsKey(variable)) {
        return data[variable]?.toString() ?? '';
      }
      return match.group(0) ?? ''; // Return original if no replacement found
    });

    return rendered;
  }
}

class RenderedTemplate {
  final String subject;
  final String htmlContent;
  final String? plainTextContent;
  final String recipientEmail;
  final String recipientName;

  RenderedTemplate({
    required this.subject,
    required this.htmlContent,
    this.plainTextContent,
    required this.recipientEmail,
    required this.recipientName,
  });
}

class TemplateValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final int variableCount;
  final List<String> variables;

  TemplateValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.variableCount,
    required this.variables,
  });
}

class TemplateStats {
  final int totalTemplates;
  final int activeTemplates;
  final int inactiveTemplates;
  final int categoriesCount;
  final Map<TemplateType, int> typeStats;
  final double averageVariableCount;

  TemplateStats({
    required this.totalTemplates,
    required this.activeTemplates,
    required this.inactiveTemplates,
    required this.categoriesCount,
    required this.typeStats,
    required this.averageVariableCount,
  });
}

class TemplateException implements Exception {
  final String message;
  TemplateException(this.message);
  
  @override
  String toString() => 'TemplateException: $message';
}
