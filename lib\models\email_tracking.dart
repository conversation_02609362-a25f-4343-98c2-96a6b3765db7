// lib/models/email_tracking.dart
import 'package:uuid/uuid.dart';

enum EmailStatus {
  sent,
  delivered,
  opened,
  clicked,
  bounced,
  unsubscribed,
  failed,
}

class EmailTracking {
  final String id;
  final String campaignId;
  final String contactId;
  final EmailStatus status;
  final DateTime sentAt;
  final DateTime? deliveredAt;
  final DateTime? openedAt;
  final DateTime? clickedAt;
  final DateTime? bouncedAt;
  final DateTime? unsubscribedAt;
  final String? bounceReason;
  final String? userAgent;
  final String? ipAddress;
  final List<String> clickedUrls;
  final int openCount;
  final int clickCount;
  final Map<String, dynamic> metadata;

  EmailTracking({
    String? id,
    required this.campaignId,
    required this.contactId,
    required this.status,
    DateTime? sentAt,
    this.deliveredAt,
    this.openedAt,
    this.clickedAt,
    this.bouncedAt,
    this.unsubscribedAt,
    this.bounceReason,
    this.userAgent,
    this.ipAddress,
    List<String>? clickedUrls,
    this.openCount = 0,
    this.clickCount = 0,
    Map<String, dynamic>? metadata,
  }) : id = id ?? const Uuid().v4(),
       sentAt = sentAt ?? DateTime.now(),
       clickedUrls = clickedUrls ?? [],
       metadata = metadata ?? {};

  EmailTracking copyWith({
    String? id,
    String? campaignId,
    String? contactId,
    EmailStatus? status,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? openedAt,
    DateTime? clickedAt,
    DateTime? bouncedAt,
    DateTime? unsubscribedAt,
    String? bounceReason,
    String? userAgent,
    String? ipAddress,
    List<String>? clickedUrls,
    int? openCount,
    int? clickCount,
    Map<String, dynamic>? metadata,
  }) {
    return EmailTracking(
      id: id ?? this.id,
      campaignId: campaignId ?? this.campaignId,
      contactId: contactId ?? this.contactId,
      status: status ?? this.status,
      sentAt: sentAt ?? this.sentAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      openedAt: openedAt ?? this.openedAt,
      clickedAt: clickedAt ?? this.clickedAt,
      bouncedAt: bouncedAt ?? this.bouncedAt,
      unsubscribedAt: unsubscribedAt ?? this.unsubscribedAt,
      bounceReason: bounceReason ?? this.bounceReason,
      userAgent: userAgent ?? this.userAgent,
      ipAddress: ipAddress ?? this.ipAddress,
      clickedUrls: clickedUrls ?? this.clickedUrls,
      openCount: openCount ?? this.openCount,
      clickCount: clickCount ?? this.clickCount,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'campaign_id': campaignId,
      'contact_id': contactId,
      'status': status.toString(),
      'sent_at': sentAt.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'opened_at': openedAt?.toIso8601String(),
      'clicked_at': clickedAt?.toIso8601String(),
      'bounced_at': bouncedAt?.toIso8601String(),
      'unsubscribed_at': unsubscribedAt?.toIso8601String(),
      'bounce_reason': bounceReason,
      'user_agent': userAgent,
      'ip_address': ipAddress,
      'clicked_urls': clickedUrls.join(','),
      'open_count': openCount,
      'click_count': clickCount,
      'metadata': metadata.isNotEmpty ? 
          metadata.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
    };
  }

  factory EmailTracking.fromMap(Map<String, dynamic> map) {
    final metadataStr = map['metadata'] as String?;
    final metadata = <String, dynamic>{};
    
    if (metadataStr != null && metadataStr.isNotEmpty) {
      for (final pair in metadataStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          metadata[parts[0]] = parts[1];
        }
      }
    }

    return EmailTracking(
      id: map['id'] as String,
      campaignId: map['campaign_id'] as String,
      contactId: map['contact_id'] as String,
      status: EmailStatus.values.firstWhere(
        (s) => s.toString() == map['status'],
        orElse: () => EmailStatus.sent,
      ),
      sentAt: DateTime.parse(map['sent_at'] as String),
      deliveredAt: map['delivered_at'] != null ? DateTime.parse(map['delivered_at'] as String) : null,
      openedAt: map['opened_at'] != null ? DateTime.parse(map['opened_at'] as String) : null,
      clickedAt: map['clicked_at'] != null ? DateTime.parse(map['clicked_at'] as String) : null,
      bouncedAt: map['bounced_at'] != null ? DateTime.parse(map['bounced_at'] as String) : null,
      unsubscribedAt: map['unsubscribed_at'] != null ? DateTime.parse(map['unsubscribed_at'] as String) : null,
      bounceReason: map['bounce_reason'] as String?,
      userAgent: map['user_agent'] as String?,
      ipAddress: map['ip_address'] as String?,
      clickedUrls: (map['clicked_urls'] as String?)?.split(',').where((url) => url.isNotEmpty).toList() ?? [],
      openCount: map['open_count'] as int? ?? 0,
      clickCount: map['click_count'] as int? ?? 0,
      metadata: metadata,
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory EmailTracking.fromJson(Map<String, dynamic> json) => EmailTracking.fromMap(json);

  @override
  String toString() {
    return 'EmailTracking(id: $id, campaignId: $campaignId, contactId: $contactId, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmailTracking && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  bool get isDelivered => status == EmailStatus.delivered || 
                         status == EmailStatus.opened || 
                         status == EmailStatus.clicked;

  bool get isOpened => status == EmailStatus.opened || status == EmailStatus.clicked;

  bool get isClicked => status == EmailStatus.clicked;

  bool get isBounced => status == EmailStatus.bounced;

  bool get isUnsubscribed => status == EmailStatus.unsubscribed;

  bool get isFailed => status == EmailStatus.failed || status == EmailStatus.bounced;

  String get statusDisplayName {
    switch (status) {
      case EmailStatus.sent:
        return 'Sent';
      case EmailStatus.delivered:
        return 'Delivered';
      case EmailStatus.opened:
        return 'Opened';
      case EmailStatus.clicked:
        return 'Clicked';
      case EmailStatus.bounced:
        return 'Bounced';
      case EmailStatus.unsubscribed:
        return 'Unsubscribed';
      case EmailStatus.failed:
        return 'Failed';
    }
  }

  Duration? get timeToOpen {
    if (openedAt != null) {
      return openedAt!.difference(sentAt);
    }
    return null;
  }

  Duration? get timeToClick {
    if (clickedAt != null) {
      return clickedAt!.difference(sentAt);
    }
    return null;
  }

  Duration? get timeToDelivery {
    if (deliveredAt != null) {
      return deliveredAt!.difference(sentAt);
    }
    return null;
  }

  bool hasClickedUrl(String url) => clickedUrls.contains(url);

  EmailTracking addClickedUrl(String url) {
    if (!clickedUrls.contains(url)) {
      return copyWith(
        clickedUrls: [...clickedUrls, url],
        clickCount: clickCount + 1,
        status: EmailStatus.clicked,
        clickedAt: DateTime.now(),
      );
    }
    return this;
  }

  EmailTracking recordOpen() {
    return copyWith(
      status: isClicked ? EmailStatus.clicked : EmailStatus.opened,
      openedAt: openedAt ?? DateTime.now(),
      openCount: openCount + 1,
    );
  }

  EmailTracking recordDelivery() {
    return copyWith(
      status: status == EmailStatus.sent ? EmailStatus.delivered : status,
      deliveredAt: deliveredAt ?? DateTime.now(),
    );
  }

  EmailTracking recordBounce(String reason) {
    return copyWith(
      status: EmailStatus.bounced,
      bouncedAt: DateTime.now(),
      bounceReason: reason,
    );
  }

  EmailTracking recordUnsubscribe() {
    return copyWith(
      status: EmailStatus.unsubscribed,
      unsubscribedAt: DateTime.now(),
    );
  }

  EmailTracking addMetadata(String key, dynamic value) {
    final newMetadata = Map<String, dynamic>.from(metadata);
    newMetadata[key] = value;
    return copyWith(metadata: newMetadata);
  }

  EmailTracking removeMetadata(String key) {
    final newMetadata = Map<String, dynamic>.from(metadata);
    newMetadata.remove(key);
    return copyWith(metadata: newMetadata);
  }

  Map<String, dynamic> getEngagementMetrics() {
    return {
      'isDelivered': isDelivered,
      'isOpened': isOpened,
      'isClicked': isClicked,
      'isBounced': isBounced,
      'isUnsubscribed': isUnsubscribed,
      'openCount': openCount,
      'clickCount': clickCount,
      'timeToOpen': timeToOpen?.inMinutes,
      'timeToClick': timeToClick?.inMinutes,
      'timeToDelivery': timeToDelivery?.inSeconds,
      'clickedUrlsCount': clickedUrls.length,
    };
  }
}
