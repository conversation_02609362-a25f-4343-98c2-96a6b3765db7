// lib/services/csv_service.dart
import 'dart:io';
import 'package:csv/csv.dart';
import '../models/recipient_model.dart';

class CsvService {
  static Future<List<Recipient>> parseCsv(File file) async {
    final content = await file.readAsString();
    final rows = const CsvToListConverter().convert(content);

    // Skip header and map valid rows
    return rows
        .skip(1)
        .where((row) => row.length >= 2)
        .map((row) {
          try {
            return Recipient.fromCsv(row);
          } catch (_) {
            return null;
          }
        })
        .whereType<Recipient>()
        .toList();
  }
}
