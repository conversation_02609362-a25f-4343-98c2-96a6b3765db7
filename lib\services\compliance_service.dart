// lib/services/compliance_service.dart
import 'dart:convert';
import '../repositories/contact_repository.dart';
import '../repositories/campaign_repository.dart';
import '../models/contact.dart';
import '../models/email_campaign.dart';

class ComplianceService {
  final ContactRepository _contactRepository = ContactRepository();
  final CampaignRepository _campaignRepository = CampaignRepository();

  // GDPR Compliance Methods
  
  /// Records consent for a contact
  Future<void> recordConsent(String contactId, ConsentType type, String source) async {
    final consent = ConsentRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      contactId: contactId,
      type: type,
      granted: true,
      timestamp: DateTime.now(),
      source: source,
      ipAddress: '', // Should be captured from request
      userAgent: '', // Should be captured from request
    );
    
    await _contactRepository.addConsentRecord(consent);
  }

  /// Withdraws consent for a contact
  Future<void> withdrawConsent(String contactId, ConsentType type, String reason) async {
    final consent = ConsentRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      contactId: contactId,
      type: type,
      granted: false,
      timestamp: DateTime.now(),
      source: 'user_withdrawal',
      ipAddress: '',
      userAgent: '',
      withdrawalReason: reason,
    );
    
    await _contactRepository.addConsentRecord(consent);
    
    // Update contact status
    final contact = await _contactRepository.getContactById(contactId);
    if (contact != null) {
      final updatedContact = contact.copyWith(
        isSubscribed: type == ConsentType.marketing ? false : contact.isSubscribed,
        unsubscribedAt: type == ConsentType.marketing ? DateTime.now() : contact.unsubscribedAt,
      );
      await _contactRepository.updateContact(updatedContact);
    }
  }

  /// Checks if contact has valid consent for specific purpose
  Future<bool> hasValidConsent(String contactId, ConsentType type) async {
    final consentRecords = await _contactRepository.getConsentRecords(contactId);
    
    // Get the latest consent record for this type
    final relevantRecords = consentRecords
        .where((record) => record.type == type)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    if (relevantRecords.isEmpty) return false;
    
    final latestConsent = relevantRecords.first;
    return latestConsent.granted;
  }

  /// Exports all data for a contact (GDPR Article 20 - Right to data portability)
  Future<Map<String, dynamic>> exportContactData(String contactId) async {
    final contact = await _contactRepository.getContactById(contactId);
    if (contact == null) {
      throw Exception('Contact not found');
    }

    final consentRecords = await _contactRepository.getConsentRecords(contactId);
    final campaigns = await _campaignRepository.getCampaignsForContact(contactId);
    
    return {
      'contact': contact.toMap(),
      'consentRecords': consentRecords.map((r) => r.toMap()).toList(),
      'campaignHistory': campaigns.map((c) => {
        'campaignId': c.id,
        'campaignName': c.name,
        'sentAt': c.scheduledAt?.toIso8601String(),
        'subject': c.subject,
      }).toList(),
      'exportedAt': DateTime.now().toIso8601String(),
      'exportedBy': 'system', // Should be actual user
    };
  }

  /// Deletes all data for a contact (GDPR Article 17 - Right to erasure)
  Future<void> deleteContactData(String contactId, String reason) async {
    // Log the deletion request
    await _logDataDeletion(contactId, reason);
    
    // Remove from campaigns
    await _campaignRepository.removeContactFromAllCampaigns(contactId);
    
    // Delete consent records
    await _contactRepository.deleteConsentRecords(contactId);
    
    // Delete contact
    await _contactRepository.deleteContact(contactId);
  }

  /// Anonymizes contact data (alternative to deletion)
  Future<void> anonymizeContactData(String contactId, String reason) async {
    final contact = await _contactRepository.getContactById(contactId);
    if (contact == null) return;

    // Log the anonymization
    await _logDataAnonymization(contactId, reason);

    // Create anonymized version
    final anonymizedContact = contact.copyWith(
      name: 'Anonymized User',
      email: 'anonymized_${DateTime.now().millisecondsSinceEpoch}@deleted.local',
      phone: '',
      company: '',
      customFields: {},
      isSubscribed: false,
      unsubscribedAt: DateTime.now(),
    );

    await _contactRepository.updateContact(anonymizedContact);
  }

  // CAN-SPAM Compliance Methods

  /// Validates email content for CAN-SPAM compliance
  Future<ComplianceValidationResult> validateEmailContent(String subject, String content, String senderInfo) async {
    List<String> violations = [];
    List<String> warnings = [];

    // Check for misleading subject line
    if (_isMisleadingSubject(subject, content)) {
      violations.add('Subject line appears to be misleading or deceptive');
    }

    // Check for required sender identification
    if (!_hasValidSenderInfo(senderInfo)) {
      violations.add('Missing or invalid sender identification information');
    }

    // Check for unsubscribe mechanism
    if (!_hasUnsubscribeLink(content)) {
      violations.add('Missing unsubscribe link or mechanism');
    }

    // Check for physical address
    if (!_hasPhysicalAddress(content)) {
      violations.add('Missing physical postal address');
    }

    // Check for promotional content indicators
    if (_hasPromotionalContent(content) && !_hasPromotionalDisclaimer(content)) {
      warnings.add('Promotional content detected but no clear disclaimer found');
    }

    return ComplianceValidationResult(
      isCompliant: violations.isEmpty,
      violations: violations,
      warnings: warnings,
      checkedAt: DateTime.now(),
    );
  }

  /// Processes unsubscribe request
  Future<void> processUnsubscribeRequest(String email, String campaignId, String reason) async {
    final contact = await _contactRepository.getContactByEmail(email);
    if (contact == null) return;

    // Record unsubscribe
    await withdrawConsent(contact.id, ConsentType.marketing, reason);

    // Log unsubscribe event
    await _logUnsubscribeEvent(contact.id, campaignId, reason);

    // Update contact status
    final updatedContact = contact.copyWith(
      isSubscribed: false,
      unsubscribedAt: DateTime.now(),
    );
    await _contactRepository.updateContact(updatedContact);
  }

  /// Generates unsubscribe link for email
  String generateUnsubscribeLink(String contactId, String campaignId) {
    final token = _generateSecureToken(contactId, campaignId);
    return 'https://your-domain.com/unsubscribe?token=$token&contact=$contactId&campaign=$campaignId';
  }

  /// Validates unsubscribe token
  Future<bool> validateUnsubscribeToken(String token, String contactId, String campaignId) async {
    final expectedToken = _generateSecureToken(contactId, campaignId);
    return token == expectedToken;
  }

  // Audit and Logging Methods

  /// Logs compliance events for audit trail
  Future<void> logComplianceEvent(ComplianceEvent event) async {
    // In a real implementation, this would write to a secure audit log
    final logEntry = {
      'eventId': event.id,
      'type': event.type.toString(),
      'contactId': event.contactId,
      'timestamp': event.timestamp.toIso8601String(),
      'details': event.details,
      'userId': event.userId,
      'ipAddress': event.ipAddress,
    };
    
    // Store in database or external audit system
    print('Compliance Event Logged: ${jsonEncode(logEntry)}');
  }

  /// Generates compliance report
  Future<Map<String, dynamic>> generateComplianceReport(DateTime startDate, DateTime endDate) async {
    final contacts = await _contactRepository.getAllContacts();
    final campaigns = await _campaignRepository.getAllCampaigns();

    int totalContacts = contacts.length;
    int subscribedContacts = contacts.where((c) => c.isSubscribed).length;
    int unsubscribedContacts = contacts.where((c) => !c.isSubscribed).length;
    
    // Calculate consent statistics
    Map<ConsentType, int> consentStats = {};
    for (final contact in contacts) {
      final consentRecords = await _contactRepository.getConsentRecords(contact.id);
      for (final record in consentRecords) {
        consentStats[record.type] = (consentStats[record.type] ?? 0) + 1;
      }
    }

    return {
      'reportPeriod': {
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
      },
      'contactStatistics': {
        'total': totalContacts,
        'subscribed': subscribedContacts,
        'unsubscribed': unsubscribedContacts,
        'subscriptionRate': totalContacts > 0 ? (subscribedContacts / totalContacts) * 100 : 0,
      },
      'consentStatistics': consentStats.map((key, value) => MapEntry(key.toString(), value)),
      'campaignCompliance': await _analyzeCampaignCompliance(campaigns),
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  // Private helper methods

  bool _isMisleadingSubject(String subject, String content) {
    // Basic check for misleading subjects
    final suspiciousWords = ['free', 'urgent', 'limited time', 'act now'];
    final subjectLower = subject.toLowerCase();
    
    return suspiciousWords.any((word) => subjectLower.contains(word)) &&
           !content.toLowerCase().contains(subjectLower);
  }

  bool _hasValidSenderInfo(String senderInfo) {
    return senderInfo.isNotEmpty && 
           senderInfo.contains('@') && 
           senderInfo.length > 5;
  }

  bool _hasUnsubscribeLink(String content) {
    return content.toLowerCase().contains('unsubscribe') ||
           content.toLowerCase().contains('opt-out') ||
           content.contains('{{unsubscribe_link}}');
  }

  bool _hasPhysicalAddress(String content) {
    // Basic check for address patterns
    final addressPatterns = [
      RegExp(r'\d+\s+\w+\s+(street|st|avenue|ave|road|rd|drive|dr)', caseSensitive: false),
      RegExp(r'\d{5}(-\d{4})?'), // ZIP code
      RegExp(r'{{company_address}}'), // Template variable
    ];
    
    return addressPatterns.any((pattern) => pattern.hasMatch(content));
  }

  bool _hasPromotionalContent(String content) {
    final promotionalWords = ['sale', 'discount', 'offer', 'deal', 'promotion', 'buy now'];
    final contentLower = content.toLowerCase();
    return promotionalWords.any((word) => contentLower.contains(word));
  }

  bool _hasPromotionalDisclaimer(String content) {
    final disclaimerWords = ['promotional', 'advertisement', 'marketing'];
    final contentLower = content.toLowerCase();
    return disclaimerWords.any((word) => contentLower.contains(word));
  }

  String _generateSecureToken(String contactId, String campaignId) {
    // In production, use proper cryptographic signing
    final data = '$contactId:$campaignId:${DateTime.now().day}';
    return data.hashCode.abs().toString();
  }

  Future<void> _logDataDeletion(String contactId, String reason) async {
    final event = ComplianceEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: ComplianceEventType.dataDeletion,
      contactId: contactId,
      timestamp: DateTime.now(),
      details: {'reason': reason},
      userId: 'system',
      ipAddress: '',
    );
    await logComplianceEvent(event);
  }

  Future<void> _logDataAnonymization(String contactId, String reason) async {
    final event = ComplianceEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: ComplianceEventType.dataAnonymization,
      contactId: contactId,
      timestamp: DateTime.now(),
      details: {'reason': reason},
      userId: 'system',
      ipAddress: '',
    );
    await logComplianceEvent(event);
  }

  Future<void> _logUnsubscribeEvent(String contactId, String campaignId, String reason) async {
    final event = ComplianceEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: ComplianceEventType.unsubscribe,
      contactId: contactId,
      timestamp: DateTime.now(),
      details: {'campaignId': campaignId, 'reason': reason},
      userId: 'system',
      ipAddress: '',
    );
    await logComplianceEvent(event);
  }

  Future<Map<String, dynamic>> _analyzeCampaignCompliance(List<EmailCampaign> campaigns) async {
    int compliantCampaigns = 0;
    int totalCampaigns = campaigns.length;
    List<String> nonCompliantCampaigns = [];

    for (final campaign in campaigns) {
      final validation = await validateEmailContent(
        campaign.subject,
        campaign.content,
        campaign.senderEmail,
      );
      
      if (validation.isCompliant) {
        compliantCampaigns++;
      } else {
        nonCompliantCampaigns.add(campaign.name);
      }
    }

    return {
      'totalCampaigns': totalCampaigns,
      'compliantCampaigns': compliantCampaigns,
      'complianceRate': totalCampaigns > 0 ? (compliantCampaigns / totalCampaigns) * 100 : 0,
      'nonCompliantCampaigns': nonCompliantCampaigns,
    };
  }
}

// Supporting classes and enums

enum ConsentType {
  marketing,
  analytics,
  functional,
  necessary,
}

enum ComplianceEventType {
  consentGranted,
  consentWithdrawn,
  dataExport,
  dataDeletion,
  dataAnonymization,
  unsubscribe,
  complianceViolation,
}

class ConsentRecord {
  final String id;
  final String contactId;
  final ConsentType type;
  final bool granted;
  final DateTime timestamp;
  final String source;
  final String ipAddress;
  final String userAgent;
  final String? withdrawalReason;

  ConsentRecord({
    required this.id,
    required this.contactId,
    required this.type,
    required this.granted,
    required this.timestamp,
    required this.source,
    required this.ipAddress,
    required this.userAgent,
    this.withdrawalReason,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'contactId': contactId,
      'type': type.toString(),
      'granted': granted,
      'timestamp': timestamp.toIso8601String(),
      'source': source,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'withdrawalReason': withdrawalReason,
    };
  }

  factory ConsentRecord.fromMap(Map<String, dynamic> map) {
    return ConsentRecord(
      id: map['id'] ?? '',
      contactId: map['contactId'] ?? '',
      type: ConsentType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => ConsentType.necessary,
      ),
      granted: map['granted'] ?? false,
      timestamp: DateTime.parse(map['timestamp'] ?? DateTime.now().toIso8601String()),
      source: map['source'] ?? '',
      ipAddress: map['ipAddress'] ?? '',
      userAgent: map['userAgent'] ?? '',
      withdrawalReason: map['withdrawalReason'],
    );
  }
}

class ComplianceEvent {
  final String id;
  final ComplianceEventType type;
  final String contactId;
  final DateTime timestamp;
  final Map<String, dynamic> details;
  final String userId;
  final String ipAddress;

  ComplianceEvent({
    required this.id,
    required this.type,
    required this.contactId,
    required this.timestamp,
    required this.details,
    required this.userId,
    required this.ipAddress,
  });
}

class ComplianceValidationResult {
  final bool isCompliant;
  final List<String> violations;
  final List<String> warnings;
  final DateTime checkedAt;

  ComplianceValidationResult({
    required this.isCompliant,
    required this.violations,
    required this.warnings,
    required this.checkedAt,
  });
}
