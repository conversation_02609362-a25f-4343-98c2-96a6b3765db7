// lib/services/pdf_service.dart
import 'dart:io';
import 'dart:ui' show Offset;
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PdfService {
  static Future<List<File>> splitPdf(File file, String outputDir) async {
    final inputBytes = await file.readAsBytes();
    final document = PdfDocument(inputBytes: inputBytes);
    final outputFiles = <File>[];

    // Ensure output directory exists
    final dir = Directory(outputDir);
    if (!dir.existsSync()) {
      dir.createSync(recursive: true);
    }

    for (int i = 0; i < document.pages.count; i++) {
      final newDoc = PdfDocument();

      // Clone the original page to the new document
      final originalPage = document.pages[i];
      final template = originalPage.createTemplate();

      final newPage = newDoc.pages.add();
      newPage.graphics.drawPdfTemplate(
        template,
        Offset(0, 0), // Top-left corner of the new page
      );

      // Save to file
      final outputFile = File('$outputDir/page_${i + 1}.pdf');
      outputFile.writeAsBytesSync(newDoc.saveSync());
      outputFiles.add(outputFile);

      newDoc.dispose();
    }

    document.dispose();
    return outputFiles;
  }
}
