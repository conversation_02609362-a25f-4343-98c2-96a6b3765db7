// lib/services/mail_service.dart
import 'dart:io';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import '../config/smtp_config.dart';

class MailService {
  static Future<void> sendMail(
    SmtpConfig config,
    String toEmail,
    File attachment,
  ) async {
    final smtpServer = SmtpServer(
      config.smtpServer,
      port: config.port,
      username: config.username,
      password: config.password,
      ssl: false,
      ignoreBadCertificate: true, // for self-signed certs
    );

    final message =
        Message()
          ..from = Address(config.username)
          ..recipients.add(toEmail)
          ..subject = 'Your PDF Document'
          ..text = 'Please find your PDF attached.'
          ..attachments = [FileAttachment(attachment)];

    try {
      final sendReport = await send(message, smtpServer);
      print('✅ Email sent: ${sendReport.toString()}');
    } on MailerException catch (e) {
      print('❌ Mailer error: ${e.message}');
      for (var p in e.problems) {
        print('Problem: ${p.code}: ${p.msg}');
      }
      rethrow; // <-- add this to let UI catch and show it
    } catch (e) {
      print('❌ General error: $e');
      rethrow; // <-- important
    }
  }
}
