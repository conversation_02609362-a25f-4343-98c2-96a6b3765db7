// lib/models/contact_group_model.dart

enum GroupType {
  static,
  dynamic,
  imported,
}

class ContactGroup {
  final String? id;
  final String name;
  final String? description;
  final GroupType type;
  final List<String> contactIds; // For static groups
  final Map<String, dynamic> criteria; // For dynamic groups
  final int contactCount;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? color;
  final bool isActive;
  final Map<String, dynamic> metadata;

  ContactGroup({
    this.id,
    required this.name,
    this.description,
    this.type = GroupType.static,
    this.contactIds = const [],
    this.criteria = const {},
    this.contactCount = 0,
    DateTime? createdAt,
    this.updatedAt,
    this.color,
    this.isActive = true,
    this.metadata = const {},
  }) : createdAt = createdAt ?? DateTime.now();

  factory ContactGroup.fromJson(Map<String, dynamic> json) {
    return ContactGroup(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: GroupType.values.firstWhere(
        (e) => e.toString() == 'GroupType.${json['type']}',
        orElse: () => GroupType.static,
      ),
      contactIds: List<String>.from(json['contactIds'] ?? []),
      criteria: Map<String, dynamic>.from(json['criteria'] ?? {}),
      contactCount: json['contactCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      color: json['color'],
      isActive: json['isActive'] ?? true,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'contactIds': contactIds,
      'criteria': criteria,
      'contactCount': contactCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'color': color,
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  ContactGroup copyWith({
    String? id,
    String? name,
    String? description,
    GroupType? type,
    List<String>? contactIds,
    Map<String, dynamic>? criteria,
    int? contactCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? color,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return ContactGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      contactIds: contactIds ?? this.contactIds,
      criteria: criteria ?? this.criteria,
      contactCount: contactCount ?? this.contactCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }
}

class ContactTag {
  final String? id;
  final String name;
  final String? description;
  final String? color;
  final int contactCount;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;

  ContactTag({
    this.id,
    required this.name,
    this.description,
    this.color,
    this.contactCount = 0,
    DateTime? createdAt,
    this.updatedAt,
    this.isActive = true,
  }) : createdAt = createdAt ?? DateTime.now();

  factory ContactTag.fromJson(Map<String, dynamic> json) {
    return ContactTag(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      color: json['color'],
      contactCount: json['contactCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'contactCount': contactCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isActive': isActive,
    };
  }

  ContactTag copyWith({
    String? id,
    String? name,
    String? description,
    String? color,
    int? contactCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return ContactTag(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      contactCount: contactCount ?? this.contactCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }
}

// Criteria for dynamic groups
class GroupCriteria {
  final String field;
  final String operator;
  final dynamic value;

  GroupCriteria({
    required this.field,
    required this.operator,
    required this.value,
  });

  factory GroupCriteria.fromJson(Map<String, dynamic> json) {
    return GroupCriteria(
      field: json['field'],
      operator: json['operator'],
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'operator': operator,
      'value': value,
    };
  }

  // Check if a contact matches this criteria
  bool matches(Map<String, dynamic> contactData) {
    final fieldValue = contactData[field];
    
    switch (operator) {
      case 'equals':
        return fieldValue == value;
      case 'not_equals':
        return fieldValue != value;
      case 'contains':
        return fieldValue?.toString().toLowerCase().contains(value.toString().toLowerCase()) ?? false;
      case 'not_contains':
        return !(fieldValue?.toString().toLowerCase().contains(value.toString().toLowerCase()) ?? false);
      case 'starts_with':
        return fieldValue?.toString().toLowerCase().startsWith(value.toString().toLowerCase()) ?? false;
      case 'ends_with':
        return fieldValue?.toString().toLowerCase().endsWith(value.toString().toLowerCase()) ?? false;
      case 'is_empty':
        return fieldValue == null || fieldValue.toString().isEmpty;
      case 'is_not_empty':
        return fieldValue != null && fieldValue.toString().isNotEmpty;
      case 'greater_than':
        if (fieldValue is num && value is num) {
          return fieldValue > value;
        }
        return false;
      case 'less_than':
        if (fieldValue is num && value is num) {
          return fieldValue < value;
        }
        return false;
      case 'has_tag':
        if (fieldValue is List) {
          return fieldValue.contains(value);
        }
        return false;
      case 'not_has_tag':
        if (fieldValue is List) {
          return !fieldValue.contains(value);
        }
        return true;
      default:
        return false;
    }
  }
}
