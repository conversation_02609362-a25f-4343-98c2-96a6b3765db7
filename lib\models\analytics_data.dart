// lib/models/analytics_data.dart

class AnalyticsOverview {
  final int totalCampaigns;
  final int totalEmailsSent;
  final double averageOpenRate;
  final double averageClickRate;
  final int totalContacts;
  final int activeContacts;
  final double bounceRate;
  final double unsubscribeRate;

  AnalyticsOverview({
    required this.totalCampaigns,
    required this.totalEmailsSent,
    required this.averageOpenRate,
    required this.averageClickRate,
    required this.totalContacts,
    required this.activeContacts,
    required this.bounceRate,
    required this.unsubscribeRate,
  });

  factory AnalyticsOverview.fromMap(Map<String, dynamic> map) {
    return AnalyticsOverview(
      totalCampaigns: map['totalCampaigns'] ?? 0,
      totalEmailsSent: map['totalEmailsSent'] ?? 0,
      averageOpenRate: (map['averageOpenRate'] ?? 0.0).toDouble(),
      averageClickRate: (map['averageClickRate'] ?? 0.0).toDouble(),
      totalContacts: map['totalContacts'] ?? 0,
      activeContacts: map['activeContacts'] ?? 0,
      bounceRate: (map['bounceRate'] ?? 0.0).toDouble(),
      unsubscribeRate: (map['unsubscribeRate'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalCampaigns': totalCampaigns,
      'totalEmailsSent': totalEmailsSent,
      'averageOpenRate': averageOpenRate,
      'averageClickRate': averageClickRate,
      'totalContacts': totalContacts,
      'activeContacts': activeContacts,
      'bounceRate': bounceRate,
      'unsubscribeRate': unsubscribeRate,
    };
  }
}

class CampaignPerformance {
  final String campaignId;
  final String campaignName;
  final DateTime sentDate;
  final int totalRecipients;
  final int delivered;
  final int opened;
  final int clicked;
  final int bounced;
  final int unsubscribed;
  final int complained;
  final double deliveryRate;
  final double openRate;
  final double clickRate;
  final double bounceRate;
  final double unsubscribeRate;
  final double complaintRate;
  final Map<String, int> hourlyOpens;
  final Map<String, int> deviceTypes;
  final Map<String, int> emailClients;

  CampaignPerformance({
    required this.campaignId,
    required this.campaignName,
    required this.sentDate,
    required this.totalRecipients,
    required this.delivered,
    required this.opened,
    required this.clicked,
    required this.bounced,
    required this.unsubscribed,
    required this.complained,
    required this.deliveryRate,
    required this.openRate,
    required this.clickRate,
    required this.bounceRate,
    required this.unsubscribeRate,
    required this.complaintRate,
    required this.hourlyOpens,
    required this.deviceTypes,
    required this.emailClients,
  });

  factory CampaignPerformance.fromMap(Map<String, dynamic> map) {
    return CampaignPerformance(
      campaignId: map['campaignId'] ?? '',
      campaignName: map['campaignName'] ?? '',
      sentDate: DateTime.parse(map['sentDate'] ?? DateTime.now().toIso8601String()),
      totalRecipients: map['totalRecipients'] ?? 0,
      delivered: map['delivered'] ?? 0,
      opened: map['opened'] ?? 0,
      clicked: map['clicked'] ?? 0,
      bounced: map['bounced'] ?? 0,
      unsubscribed: map['unsubscribed'] ?? 0,
      complained: map['complained'] ?? 0,
      deliveryRate: (map['deliveryRate'] ?? 0.0).toDouble(),
      openRate: (map['openRate'] ?? 0.0).toDouble(),
      clickRate: (map['clickRate'] ?? 0.0).toDouble(),
      bounceRate: (map['bounceRate'] ?? 0.0).toDouble(),
      unsubscribeRate: (map['unsubscribeRate'] ?? 0.0).toDouble(),
      complaintRate: (map['complaintRate'] ?? 0.0).toDouble(),
      hourlyOpens: Map<String, int>.from(map['hourlyOpens'] ?? {}),
      deviceTypes: Map<String, int>.from(map['deviceTypes'] ?? {}),
      emailClients: Map<String, int>.from(map['emailClients'] ?? {}),
    );
  }
}

class ContactEngagement {
  final String contactId;
  final String email;
  final String name;
  final int totalEmailsReceived;
  final int totalEmailsOpened;
  final int totalEmailsClicked;
  final int totalBounces;
  final int totalUnsubscribes;
  final DateTime firstEngagement;
  final DateTime lastEngagement;
  final double engagementScore;
  final List<String> preferredTopics;
  final Map<String, int> campaignEngagement;

  ContactEngagement({
    required this.contactId,
    required this.email,
    required this.name,
    required this.totalEmailsReceived,
    required this.totalEmailsOpened,
    required this.totalEmailsClicked,
    required this.totalBounces,
    required this.totalUnsubscribes,
    required this.firstEngagement,
    required this.lastEngagement,
    required this.engagementScore,
    required this.preferredTopics,
    required this.campaignEngagement,
  });

  factory ContactEngagement.fromMap(Map<String, dynamic> map) {
    return ContactEngagement(
      contactId: map['contactId'] ?? '',
      email: map['email'] ?? '',
      name: map['name'] ?? '',
      totalEmailsReceived: map['totalEmailsReceived'] ?? 0,
      totalEmailsOpened: map['totalEmailsOpened'] ?? 0,
      totalEmailsClicked: map['totalEmailsClicked'] ?? 0,
      totalBounces: map['totalBounces'] ?? 0,
      totalUnsubscribes: map['totalUnsubscribes'] ?? 0,
      firstEngagement: DateTime.parse(map['firstEngagement'] ?? DateTime.now().toIso8601String()),
      lastEngagement: DateTime.parse(map['lastEngagement'] ?? DateTime.now().toIso8601String()),
      engagementScore: (map['engagementScore'] ?? 0.0).toDouble(),
      preferredTopics: List<String>.from(map['preferredTopics'] ?? []),
      campaignEngagement: Map<String, int>.from(map['campaignEngagement'] ?? {}),
    );
  }
}

class TimeSeriesData {
  final DateTime date;
  final int emailsSent;
  final int emailsDelivered;
  final int emailsOpened;
  final int emailsClicked;
  final int emailsBounced;
  final int unsubscribes;

  TimeSeriesData({
    required this.date,
    required this.emailsSent,
    required this.emailsDelivered,
    required this.emailsOpened,
    required this.emailsClicked,
    required this.emailsBounced,
    required this.unsubscribes,
  });

  factory TimeSeriesData.fromMap(Map<String, dynamic> map) {
    return TimeSeriesData(
      date: DateTime.parse(map['date'] ?? DateTime.now().toIso8601String()),
      emailsSent: map['emailsSent'] ?? 0,
      emailsDelivered: map['emailsDelivered'] ?? 0,
      emailsOpened: map['emailsOpened'] ?? 0,
      emailsClicked: map['emailsClicked'] ?? 0,
      emailsBounced: map['emailsBounced'] ?? 0,
      unsubscribes: map['unsubscribes'] ?? 0,
    );
  }

  double get deliveryRate => emailsSent > 0 ? (emailsDelivered / emailsSent) * 100 : 0.0;
  double get openRate => emailsDelivered > 0 ? (emailsOpened / emailsDelivered) * 100 : 0.0;
  double get clickRate => emailsDelivered > 0 ? (emailsClicked / emailsDelivered) * 100 : 0.0;
  double get bounceRate => emailsSent > 0 ? (emailsBounced / emailsSent) * 100 : 0.0;
}

class ReportData {
  final String reportId;
  final String reportType;
  final String period;
  final DateTime generatedAt;
  final Map<String, dynamic> data;
  final String format;
  final String filePath;

  ReportData({
    required this.reportId,
    required this.reportType,
    required this.period,
    required this.generatedAt,
    required this.data,
    required this.format,
    required this.filePath,
  });

  factory ReportData.fromMap(Map<String, dynamic> map) {
    return ReportData(
      reportId: map['reportId'] ?? '',
      reportType: map['reportType'] ?? '',
      period: map['period'] ?? '',
      generatedAt: DateTime.parse(map['generatedAt'] ?? DateTime.now().toIso8601String()),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      format: map['format'] ?? 'json',
      filePath: map['filePath'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'reportId': reportId,
      'reportType': reportType,
      'period': period,
      'generatedAt': generatedAt.toIso8601String(),
      'data': data,
      'format': format,
      'filePath': filePath,
    };
  }
}

enum ReportType {
  overview,
  campaign,
  contact,
  performance,
  delivery,
  engagement,
}

enum ExportFormat {
  json,
  csv,
  pdf,
  excel,
}

class AnalyticsFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String>? campaignIds;
  final List<String>? contactGroups;
  final List<String>? tags;
  final String? emailServiceProvider;
  final double? minEngagementScore;
  final double? maxEngagementScore;

  AnalyticsFilter({
    this.startDate,
    this.endDate,
    this.campaignIds,
    this.contactGroups,
    this.tags,
    this.emailServiceProvider,
    this.minEngagementScore,
    this.maxEngagementScore,
  });

  Map<String, dynamic> toMap() {
    return {
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'campaignIds': campaignIds,
      'contactGroups': contactGroups,
      'tags': tags,
      'emailServiceProvider': emailServiceProvider,
      'minEngagementScore': minEngagementScore,
      'maxEngagementScore': maxEngagementScore,
    };
  }
}
