// lib/screens/contacts/contact_detail_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/contact_provider.dart';
import '../../models/recipient_model.dart';
import 'add_contact_screen.dart';

class ContactDetailScreen extends StatefulWidget {
  final Recipient contact;

  const ContactDetailScreen({super.key, required this.contact});

  @override
  State<ContactDetailScreen> createState() => _ContactDetailScreenState();
}

class _ContactDetailScreenState extends State<ContactDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.contact.name.isNotEmpty ? widget.contact.name : 'Contact Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editContact(),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'delete', child: Text('Delete Contact')),
              const PopupMenuItem(value: 'add_to_group', child: Text('Add to Group')),
              const PopupMenuItem(value: 'send_email', child: Text('Send Email')),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Contact Header
            _buildContactHeader(),
            const SizedBox(height: 24),
            
            // Contact Information
            _buildContactInfo(),
            const SizedBox(height: 24),
            
            // Tags Section
            _buildTagsSection(),
            const SizedBox(height: 24),
            
            // Groups Section
            _buildGroupsSection(),
            const SizedBox(height: 24),
            
            // Activity Section
            _buildActivitySection(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _editContact(),
        child: const Icon(Icons.edit),
      ),
    );
  }

  Widget _buildContactHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Theme.of(context).primaryColor,
              child: Text(
                widget.contact.name.isNotEmpty 
                    ? widget.contact.name[0].toUpperCase() 
                    : 'U',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.contact.name.isNotEmpty ? widget.contact.name : 'Unknown',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.contact.email,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  if (widget.contact.company?.isNotEmpty == true) ...[
                    const SizedBox(height: 4),
                    Text(
                      widget.contact.company!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: widget.contact.isSubscribed 
                        ? Colors.green.withOpacity(0.1)
                        : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    widget.contact.isSubscribed ? 'Subscribed' : 'Unsubscribed',
                    style: TextStyle(
                      color: widget.contact.isSubscribed ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Added ${_formatDate(widget.contact.createdAt)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Email', widget.contact.email, Icons.email),
            if (widget.contact.phone?.isNotEmpty == true)
              _buildInfoRow('Phone', widget.contact.phone!, Icons.phone),
            if (widget.contact.company?.isNotEmpty == true)
              _buildInfoRow('Company', widget.contact.company!, Icons.business),
            if (widget.contact.position?.isNotEmpty == true)
              _buildInfoRow('Position', widget.contact.position!, Icons.work),
            if (widget.contact.website?.isNotEmpty == true)
              _buildInfoRow('Website', widget.contact.website!, Icons.web),
            if (widget.contact.address?.isNotEmpty == true)
              _buildInfoRow('Address', widget.contact.address!, Icons.location_on),
            if (widget.contact.notes?.isNotEmpty == true)
              _buildInfoRow('Notes', widget.contact.notes!, Icons.note),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tags',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                TextButton.icon(
                  onPressed: () => _showAddTagDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Tag'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (widget.contact.tags.isEmpty)
              Text(
                'No tags assigned',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: widget.contact.tags.map((tag) {
                  return Chip(
                    label: Text(tag),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeTag(tag),
                    backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildGroupsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Groups',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                TextButton.icon(
                  onPressed: () => _showAddToGroupDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add to Group'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (widget.contact.groups.isEmpty)
              Text(
                'Not in any groups',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              )
            else
              Column(
                children: widget.contact.groups.map((group) {
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: const Icon(Icons.group),
                    title: Text(group),
                    trailing: IconButton(
                      icon: const Icon(Icons.remove_circle_outline),
                      onPressed: () => _removeFromGroup(group),
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            // TODO: Implement activity tracking
            Text(
              'No recent activity',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _editContact() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddContactScreen(contact: widget.contact),
      ),
    ).then((_) {
      // Refresh contact data if needed
      setState(() {});
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'delete':
        _showDeleteDialog();
        break;
      case 'add_to_group':
        _showAddToGroupDialog();
        break;
      case 'send_email':
        _sendEmail();
        break;
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Contact'),
        content: Text('Are you sure you want to delete ${widget.contact.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ContactProvider>().deleteContact(widget.contact.id!);
              Navigator.of(context).pop(); // Go back to contacts list
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showAddTagDialog() {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Tag'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter tag name',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                _addTag(controller.text);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showAddToGroupDialog() {
    // TODO: Implement add to group dialog with available groups
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add to group - Coming soon')),
    );
  }

  void _addTag(String tag) {
    // TODO: Implement add tag functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Added tag: $tag')),
    );
  }

  void _removeTag(String tag) {
    // TODO: Implement remove tag functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Removed tag: $tag')),
    );
  }

  void _removeFromGroup(String group) {
    // TODO: Implement remove from group functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Removed from group: $group')),
    );
  }

  void _sendEmail() {
    // TODO: Implement send email functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Send email to ${widget.contact.email} - Coming soon')),
    );
  }
}
