// lib/models/user_model.dart

enum UserRole { admin, manager, user, viewer }

enum UserStatus { active, inactive, suspended, pending }

class User {
  final String? id;
  final String email;
  final String name;
  final String? company;
  final UserRole role;
  final UserStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic> preferences;
  final List<String> permissions;
  final String? avatarUrl;
  final String? phone;
  final String? timezone;

  User({
    this.id,
    required this.email,
    required this.name,
    this.company,
    this.role = UserRole.user,
    this.status = UserStatus.active,
    DateTime? createdAt,
    this.updatedAt,
    this.lastLoginAt,
    this.preferences = const {},
    this.permissions = const [],
    this.avatarUrl,
    this.phone,
    this.timezone,
  }) : createdAt = createdAt ?? DateTime.now();

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      company: json['company'],
      role: UserRole.values.firstWhere(
        (e) => e.toString() == 'UserRole.${json['role']}',
        orElse: () => UserRole.user,
      ),
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == 'UserStatus.${json['status']}',
        orElse: () => UserStatus.active,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      lastLoginAt:
          json['lastLoginAt'] != null
              ? DateTime.parse(json['lastLoginAt'])
              : null,
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
      permissions: List<String>.from(json['permissions'] ?? []),
      avatarUrl: json['avatarUrl'],
      phone: json['phone'],
      timezone: json['timezone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'company': company,
      'role': role.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'preferences': preferences,
      'permissions': permissions,
      'avatarUrl': avatarUrl,
      'phone': phone,
      'timezone': timezone,
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? company,
    UserRole? role,
    UserStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
    List<String>? permissions,
    String? avatarUrl,
    String? phone,
    String? timezone,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      company: company ?? this.company,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      permissions: permissions ?? this.permissions,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      phone: phone ?? this.phone,
      timezone: timezone ?? this.timezone,
    );
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission) || role == UserRole.admin;
  }

  bool canManageCampaigns() {
    return hasPermission('manage_campaigns') ||
        role == UserRole.admin ||
        role == UserRole.manager;
  }

  bool canManageContacts() {
    return hasPermission('manage_contacts') ||
        role == UserRole.admin ||
        role == UserRole.manager;
  }

  bool canManageTemplates() {
    return hasPermission('manage_templates') ||
        role == UserRole.admin ||
        role == UserRole.manager;
  }

  bool canViewAnalytics() {
    return hasPermission('view_analytics') || role != UserRole.viewer;
  }

  bool canManageUsers() {
    return hasPermission('manage_users') || role == UserRole.admin;
  }
}

class AuthSession {
  final String? id;
  final String userId;
  final String token;
  final DateTime createdAt;
  final DateTime expiresAt;
  final String? deviceInfo;
  final String? ipAddress;
  final bool isActive;

  AuthSession({
    this.id,
    required this.userId,
    required this.token,
    DateTime? createdAt,
    DateTime? expiresAt,
    this.deviceInfo,
    this.ipAddress,
    this.isActive = true,
  }) : createdAt = createdAt ?? DateTime.now(),
       expiresAt = expiresAt ?? DateTime.now().add(const Duration(days: 30));

  factory AuthSession.fromJson(Map<String, dynamic> json) {
    return AuthSession(
      id: json['id'],
      userId: json['userId'],
      token: json['token'],
      createdAt: DateTime.parse(json['createdAt']),
      expiresAt: DateTime.parse(json['expiresAt']),
      deviceInfo: json['deviceInfo'],
      ipAddress: json['ipAddress'],
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'token': token,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'deviceInfo': deviceInfo,
      'ipAddress': ipAddress,
      'isActive': isActive,
    };
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isValid => isActive && !isExpired;

  AuthSession copyWith({
    String? id,
    String? userId,
    String? token,
    DateTime? createdAt,
    DateTime? expiresAt,
    String? deviceInfo,
    String? ipAddress,
    bool? isActive,
  }) {
    return AuthSession(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      token: token ?? this.token,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      ipAddress: ipAddress ?? this.ipAddress,
      isActive: isActive ?? this.isActive,
    );
  }
}
