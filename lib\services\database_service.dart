// lib/services/database_service.dart
import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'email_marketing.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create email_campaigns table
    await db.execute('''
      CREATE TABLE email_campaigns (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        subject TEXT NOT NULL,
        content TEXT NOT NULL,
        template_id TEXT,
        template_name TEXT,
        sender_email TEXT,
        sender_name TEXT,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        recipient_count INTEGER DEFAULT 0,
        sent_count INTEGER DEFAULT 0,
        delivered_count INTEGER DEFAULT 0,
        open_count INTEGER DEFAULT 0,
        click_count INTEGER DEFAULT 0,
        bounce_count INTEGER DEFAULT 0,
        unsubscribe_count INTEGER DEFAULT 0,
        progress REAL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        scheduled_at TEXT,
        sent_at TEXT,
        completed_at TEXT,
        error_message TEXT,
        settings TEXT,
        personalization_data TEXT
      )
    ''');

    // Create contacts table
    await db.execute('''
      CREATE TABLE contacts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        company TEXT,
        position TEXT,
        phone TEXT,
        website TEXT,
        address TEXT,
        notes TEXT,
        tags TEXT,
        groups TEXT,
        custom_fields TEXT,
        is_subscribed INTEGER DEFAULT 1,
        subscription_date TEXT,
        unsubscribe_date TEXT,
        unsubscribe_token TEXT,
        consent_given INTEGER DEFAULT 0,
        consent_date TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create email_tracking table
    await db.execute('''
      CREATE TABLE email_tracking (
        id TEXT PRIMARY KEY,
        campaign_id TEXT NOT NULL,
        contact_id TEXT NOT NULL,
        status TEXT NOT NULL,
        sent_at TEXT NOT NULL,
        delivered_at TEXT,
        opened_at TEXT,
        clicked_at TEXT,
        bounced_at TEXT,
        unsubscribed_at TEXT,
        bounce_reason TEXT,
        user_agent TEXT,
        ip_address TEXT,
        clicked_urls TEXT,
        open_count INTEGER DEFAULT 0,
        click_count INTEGER DEFAULT 0,
        metadata TEXT,
        FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
        FOREIGN KEY (contact_id) REFERENCES contacts (id)
      )
    ''');

    // Create email_templates table
    await db.execute('''
      CREATE TABLE email_templates (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        subject TEXT NOT NULL,
        html_content TEXT NOT NULL,
        plain_text_content TEXT,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        description TEXT,
        tags TEXT,
        variables TEXT,
        settings TEXT,
        preview_text TEXT,
        thumbnail_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        created_by TEXT,
        updated_by TEXT,
        usage_count INTEGER DEFAULT 0,
        last_used_at TEXT
      )
    ''');

    // Create users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        company TEXT,
        role TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        last_login_at TEXT,
        preferences TEXT,
        metadata TEXT
      )
    ''');

    // Create email_service_configs table
    await db.execute('''
      CREATE TABLE email_service_configs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        provider TEXT NOT NULL,
        from_email TEXT NOT NULL,
        from_name TEXT NOT NULL,
        api_key TEXT,
        api_secret TEXT,
        smtp_host TEXT,
        smtp_port INTEGER,
        smtp_username TEXT,
        smtp_password TEXT,
        is_active INTEGER DEFAULT 1,
        is_default INTEGER DEFAULT 0,
        settings TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_contacts_email ON contacts(email)');
    await db.execute('CREATE INDEX idx_contacts_subscribed ON contacts(is_subscribed)');
    await db.execute('CREATE INDEX idx_campaigns_status ON email_campaigns(status)');
    await db.execute('CREATE INDEX idx_campaigns_created ON email_campaigns(created_at)');
    await db.execute('CREATE INDEX idx_tracking_campaign ON email_tracking(campaign_id)');
    await db.execute('CREATE INDEX idx_tracking_contact ON email_tracking(contact_id)');
    await db.execute('CREATE INDEX idx_tracking_status ON email_tracking(status)');
    await db.execute('CREATE INDEX idx_templates_status ON email_templates(status)');
    await db.execute('CREATE INDEX idx_templates_type ON email_templates(type)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'email_marketing.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  // Helper methods for common operations
  Future<List<Map<String, dynamic>>> query(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      distinct: distinct,
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      groupBy: groupBy,
      having: having,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  Future<int> insert(String table, Map<String, dynamic> values) async {
    final db = await database;
    return await db.insert(table, values);
  }

  Future<int> update(
    String table,
    Map<String, dynamic> values, {
    String? where,
    List<Object?>? whereArgs,
  }) async {
    final db = await database;
    return await db.update(table, values, where: where, whereArgs: whereArgs);
  }

  Future<int> delete(
    String table, {
    String? where,
    List<Object?>? whereArgs,
  }) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<Object?>? arguments,
  ]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  Future<int> rawInsert(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawInsert(sql, arguments);
  }

  Future<int> rawUpdate(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawUpdate(sql, arguments);
  }

  Future<int> rawDelete(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawDelete(sql, arguments);
  }

  Future<void> execute(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    await db.execute(sql, arguments);
  }

  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    final db = await database;
    return await db.transaction(action);
  }

  Future<Batch> batch() async {
    final db = await database;
    return db.batch();
  }

  // Database health check
  Future<bool> isHealthy() async {
    try {
      final db = await database;
      await db.rawQuery('SELECT 1');
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get database info
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final db = await database;
      final version = await db.getVersion();
      final path = db.path;
      
      // Get table counts
      final campaignCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM email_campaigns')
      ) ?? 0;
      
      final contactCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM contacts')
      ) ?? 0;
      
      final trackingCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM email_tracking')
      ) ?? 0;
      
      final templateCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM email_templates')
      ) ?? 0;

      return {
        'version': version,
        'path': path,
        'isHealthy': await isHealthy(),
        'tables': {
          'campaigns': campaignCount,
          'contacts': contactCount,
          'tracking': trackingCount,
          'templates': templateCount,
        },
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'isHealthy': false,
      };
    }
  }
}
