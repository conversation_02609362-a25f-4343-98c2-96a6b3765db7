// lib/models/email_service_config.dart
import 'package:uuid/uuid.dart';

enum EmailProvider {
  sendgrid,
  mailgun,
  awsSes,
  smtp,
}

class EmailServiceConfig {
  final String id;
  final String name;
  final EmailProvider provider;
  final String fromEmail;
  final String fromName;
  final String? apiKey;
  final String? apiSecret;
  final String? smtpHost;
  final int? smtpPort;
  final String? smtpUsername;
  final String? smtpPassword;
  final bool isActive;
  final bool isDefault;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime? updatedAt;

  EmailServiceConfig({
    String? id,
    required this.name,
    required this.provider,
    required this.fromEmail,
    required this.fromName,
    this.apiKey,
    this.apiSecret,
    this.smtpHost,
    this.smtpPort,
    this.smtpUsername,
    this.smtpPassword,
    this.isActive = true,
    this.isDefault = false,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    this.updatedAt,
  }) : id = id ?? const Uuid().v4(),
       settings = settings ?? {},
       createdAt = createdAt ?? DateTime.now();

  EmailServiceConfig copyWith({
    String? id,
    String? name,
    EmailProvider? provider,
    String? fromEmail,
    String? fromName,
    String? apiKey,
    String? apiSecret,
    String? smtpHost,
    int? smtpPort,
    String? smtpUsername,
    String? smtpPassword,
    bool? isActive,
    bool? isDefault,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmailServiceConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      provider: provider ?? this.provider,
      fromEmail: fromEmail ?? this.fromEmail,
      fromName: fromName ?? this.fromName,
      apiKey: apiKey ?? this.apiKey,
      apiSecret: apiSecret ?? this.apiSecret,
      smtpHost: smtpHost ?? this.smtpHost,
      smtpPort: smtpPort ?? this.smtpPort,
      smtpUsername: smtpUsername ?? this.smtpUsername,
      smtpPassword: smtpPassword ?? this.smtpPassword,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'provider': provider.toString(),
      'from_email': fromEmail,
      'from_name': fromName,
      'api_key': apiKey,
      'api_secret': apiSecret,
      'smtp_host': smtpHost,
      'smtp_port': smtpPort,
      'smtp_username': smtpUsername,
      'smtp_password': smtpPassword,
      'is_active': isActive ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'settings': settings.isNotEmpty ? 
          settings.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory EmailServiceConfig.fromMap(Map<String, dynamic> map) {
    final settingsStr = map['settings'] as String?;
    final settings = <String, dynamic>{};
    
    if (settingsStr != null && settingsStr.isNotEmpty) {
      for (final pair in settingsStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          settings[parts[0]] = parts[1];
        }
      }
    }

    return EmailServiceConfig(
      id: map['id'] as String,
      name: map['name'] as String,
      provider: EmailProvider.values.firstWhere(
        (p) => p.toString() == map['provider'],
        orElse: () => EmailProvider.smtp,
      ),
      fromEmail: map['from_email'] as String,
      fromName: map['from_name'] as String,
      apiKey: map['api_key'] as String?,
      apiSecret: map['api_secret'] as String?,
      smtpHost: map['smtp_host'] as String?,
      smtpPort: map['smtp_port'] as int?,
      smtpUsername: map['smtp_username'] as String?,
      smtpPassword: map['smtp_password'] as String?,
      isActive: (map['is_active'] as int?) == 1,
      isDefault: (map['is_default'] as int?) == 1,
      settings: settings,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory EmailServiceConfig.fromJson(Map<String, dynamic> json) => EmailServiceConfig.fromMap(json);

  @override
  String toString() {
    return 'EmailServiceConfig(id: $id, name: $name, provider: $provider, fromEmail: $fromEmail)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmailServiceConfig && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  String get providerDisplayName {
    switch (provider) {
      case EmailProvider.sendgrid:
        return 'SendGrid';
      case EmailProvider.mailgun:
        return 'Mailgun';
      case EmailProvider.awsSes:
        return 'AWS SES';
      case EmailProvider.smtp:
        return 'SMTP';
    }
  }

  String get statusDisplayName => isActive ? 'Active' : 'Inactive';

  bool get requiresApiKey => provider != EmailProvider.smtp;

  bool get requiresSmtpSettings => provider == EmailProvider.smtp;

  bool get isConfigured {
    switch (provider) {
      case EmailProvider.sendgrid:
        return apiKey != null && apiKey!.isNotEmpty;
      case EmailProvider.mailgun:
        return apiKey != null && apiKey!.isNotEmpty;
      case EmailProvider.awsSes:
        return apiKey != null && apiKey!.isNotEmpty && 
               apiSecret != null && apiSecret!.isNotEmpty;
      case EmailProvider.smtp:
        return smtpHost != null && smtpHost!.isNotEmpty &&
               smtpPort != null && smtpPort! > 0 &&
               smtpUsername != null && smtpUsername!.isNotEmpty &&
               smtpPassword != null && smtpPassword!.isNotEmpty;
    }
  }

  EmailServiceConfig activate() => copyWith(isActive: true, updatedAt: DateTime.now());
  EmailServiceConfig deactivate() => copyWith(isActive: false, isDefault: false, updatedAt: DateTime.now());
  EmailServiceConfig makeDefault() => copyWith(isDefault: true, isActive: true, updatedAt: DateTime.now());
  EmailServiceConfig removeDefault() => copyWith(isDefault: false, updatedAt: DateTime.now());

  EmailServiceConfig updateSetting(String key, dynamic value) {
    final newSettings = Map<String, dynamic>.from(settings);
    newSettings[key] = value;
    return copyWith(settings: newSettings, updatedAt: DateTime.now());
  }

  EmailServiceConfig removeSetting(String key) {
    final newSettings = Map<String, dynamic>.from(settings);
    newSettings.remove(key);
    return copyWith(settings: newSettings, updatedAt: DateTime.now());
  }

  Map<String, dynamic> getConnectionInfo() {
    switch (provider) {
      case EmailProvider.sendgrid:
        return {
          'provider': 'SendGrid',
          'apiUrl': 'https://api.sendgrid.com/v3/mail/send',
          'hasApiKey': apiKey != null && apiKey!.isNotEmpty,
        };
      case EmailProvider.mailgun:
        return {
          'provider': 'Mailgun',
          'apiUrl': 'https://api.mailgun.net/v3',
          'hasApiKey': apiKey != null && apiKey!.isNotEmpty,
        };
      case EmailProvider.awsSes:
        return {
          'provider': 'AWS SES',
          'region': settings['region'] ?? 'us-east-1',
          'hasCredentials': apiKey != null && apiKey!.isNotEmpty && 
                           apiSecret != null && apiSecret!.isNotEmpty,
        };
      case EmailProvider.smtp:
        return {
          'provider': 'SMTP',
          'host': smtpHost,
          'port': smtpPort,
          'hasCredentials': smtpUsername != null && smtpUsername!.isNotEmpty &&
                           smtpPassword != null && smtpPassword!.isNotEmpty,
        };
    }
  }

  Map<String, dynamic> getSecurityInfo() {
    return {
      'id': id,
      'name': name,
      'provider': providerDisplayName,
      'fromEmail': fromEmail,
      'isConfigured': isConfigured,
      'isActive': isActive,
      'isDefault': isDefault,
      'hasApiKey': apiKey != null && apiKey!.isNotEmpty,
      'hasApiSecret': apiSecret != null && apiSecret!.isNotEmpty,
      'hasSmtpCredentials': smtpUsername != null && smtpUsername!.isNotEmpty &&
                           smtpPassword != null && smtpPassword!.isNotEmpty,
    };
  }

  // Validation methods
  List<String> validate() {
    final errors = <String>[];

    if (name.isEmpty) {
      errors.add('Name is required');
    }

    if (fromEmail.isEmpty || !fromEmail.contains('@')) {
      errors.add('Valid from email is required');
    }

    if (fromName.isEmpty) {
      errors.add('From name is required');
    }

    switch (provider) {
      case EmailProvider.sendgrid:
        if (apiKey == null || apiKey!.isEmpty) {
          errors.add('SendGrid API key is required');
        }
        break;
      case EmailProvider.mailgun:
        if (apiKey == null || apiKey!.isEmpty) {
          errors.add('Mailgun API key is required');
        }
        break;
      case EmailProvider.awsSes:
        if (apiKey == null || apiKey!.isEmpty) {
          errors.add('AWS Access Key ID is required');
        }
        if (apiSecret == null || apiSecret!.isEmpty) {
          errors.add('AWS Secret Access Key is required');
        }
        break;
      case EmailProvider.smtp:
        if (smtpHost == null || smtpHost!.isEmpty) {
          errors.add('SMTP host is required');
        }
        if (smtpPort == null || smtpPort! <= 0) {
          errors.add('Valid SMTP port is required');
        }
        if (smtpUsername == null || smtpUsername!.isEmpty) {
          errors.add('SMTP username is required');
        }
        if (smtpPassword == null || smtpPassword!.isEmpty) {
          errors.add('SMTP password is required');
        }
        break;
    }

    return errors;
  }

  bool get isValid => validate().isEmpty;

  // Factory methods for common configurations
  factory EmailServiceConfig.sendGrid({
    required String name,
    required String fromEmail,
    required String fromName,
    required String apiKey,
  }) {
    return EmailServiceConfig(
      name: name,
      provider: EmailProvider.sendgrid,
      fromEmail: fromEmail,
      fromName: fromName,
      apiKey: apiKey,
    );
  }

  factory EmailServiceConfig.mailgun({
    required String name,
    required String fromEmail,
    required String fromName,
    required String apiKey,
    String? domain,
  }) {
    return EmailServiceConfig(
      name: name,
      provider: EmailProvider.mailgun,
      fromEmail: fromEmail,
      fromName: fromName,
      apiKey: apiKey,
      settings: domain != null ? {'domain': domain} : {},
    );
  }

  factory EmailServiceConfig.awsSes({
    required String name,
    required String fromEmail,
    required String fromName,
    required String accessKeyId,
    required String secretAccessKey,
    String region = 'us-east-1',
  }) {
    return EmailServiceConfig(
      name: name,
      provider: EmailProvider.awsSes,
      fromEmail: fromEmail,
      fromName: fromName,
      apiKey: accessKeyId,
      apiSecret: secretAccessKey,
      settings: {'region': region},
    );
  }

  factory EmailServiceConfig.smtp({
    required String name,
    required String fromEmail,
    required String fromName,
    required String host,
    required int port,
    required String username,
    required String password,
    bool useTls = true,
  }) {
    return EmailServiceConfig(
      name: name,
      provider: EmailProvider.smtp,
      fromEmail: fromEmail,
      fromName: fromName,
      smtpHost: host,
      smtpPort: port,
      smtpUsername: username,
      smtpPassword: password,
      settings: {'useTls': useTls},
    );
  }
}
