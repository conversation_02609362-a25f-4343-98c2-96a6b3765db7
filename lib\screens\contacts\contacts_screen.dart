// lib/screens/contacts/contacts_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/contact_provider.dart';
import '../../models/recipient_model.dart';
import '../../models/contact_group_model.dart';
import 'contact_detail_screen.dart';
import 'add_contact_screen.dart';
import 'import_contacts_screen.dart';

class ContactsScreen extends StatefulWidget {
  const ContactsScreen({super.key});

  @override
  State<ContactsScreen> createState() => _ContactsScreenState();
}

class _ContactsScreenState extends State<ContactsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ContactProvider>().loadContacts();
      context.read<ContactProvider>().loadGroups();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Contacts'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.people), text: 'All Contacts'),
            Tab(icon: Icon(Icons.group), text: 'Groups'),
            Tab(icon: Icon(Icons.label), text: 'Tags'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddContactDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.upload_file),
            onPressed: () => _showImportDialog(),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'export', child: Text('Export Contacts')),
              const PopupMenuItem(value: 'bulk_delete', child: Text('Bulk Delete')),
              const PopupMenuItem(value: 'settings', child: Text('Settings')),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search contacts...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          context.read<ContactProvider>().searchContacts('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                context.read<ContactProvider>().searchContacts(value);
              },
            ),
          ),
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildContactsList(),
                _buildGroupsList(),
                _buildTagsList(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddContactDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContactsList() {
    return Consumer<ContactProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text('Error: ${provider.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => provider.loadContacts(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final contacts = provider.contacts;
        if (contacts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No contacts found',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Add your first contact to get started',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _showAddContactDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Contact'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: contacts.length,
          itemBuilder: (context, index) {
            final contact = contacts[index];
            return _buildContactTile(contact);
          },
        );
      },
    );
  }

  Widget _buildContactTile(Recipient contact) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor,
          child: Text(
            contact.name.isNotEmpty ? contact.name[0].toUpperCase() : 'U',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(contact.name.isNotEmpty ? contact.name : 'Unknown'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(contact.email),
            if (contact.company?.isNotEmpty == true)
              Text(contact.company!, style: TextStyle(color: Colors.grey[600])),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (contact.tags.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${contact.tags.length} tags',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 12,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              onSelected: (value) => _handleContactAction(value, contact),
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
                const PopupMenuItem(value: 'add_to_group', child: Text('Add to Group')),
              ],
            ),
          ],
        ),
        onTap: () => _showContactDetail(contact),
      ),
    );
  }

  Widget _buildGroupsList() {
    return Consumer<ContactProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final groups = provider.groups;
        if (groups.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.group_outlined, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No groups found',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Create groups to organize your contacts',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _showCreateGroupDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Create Group'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: groups.length,
          itemBuilder: (context, index) {
            final group = groups[index];
            return _buildGroupTile(group);
          },
        );
      },
    );
  }

  Widget _buildGroupTile(ContactGroup group) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: group.color != null 
              ? Color(int.parse(group.color!.replaceFirst('#', '0xff')))
              : Theme.of(context).primaryColor,
          child: const Icon(Icons.group, color: Colors.white),
        ),
        title: Text(group.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (group.description?.isNotEmpty == true)
              Text(group.description!),
            Text('${group.contactCount} contacts • ${group.type.toString().split('.').last}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleGroupAction(value, group),
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'edit', child: Text('Edit')),
            const PopupMenuItem(value: 'delete', child: Text('Delete')),
            const PopupMenuItem(value: 'view_contacts', child: Text('View Contacts')),
          ],
        ),
        onTap: () => _showGroupDetail(group),
      ),
    );
  }

  Widget _buildTagsList() {
    return Consumer<ContactProvider>(
      builder: (context, provider, child) {
        final tags = provider.availableTags;
        if (tags.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.label_outline, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No tags found',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Tags will appear here when you add them to contacts',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: tags.length,
          itemBuilder: (context, index) {
            final tag = tags[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(context).primaryColor,
                  child: const Icon(Icons.label, color: Colors.white),
                ),
                title: Text(tag),
                trailing: PopupMenuButton<String>(
                  onSelected: (value) => _handleTagAction(value, tag),
                  itemBuilder: (context) => [
                    const PopupMenuItem(value: 'rename', child: Text('Rename')),
                    const PopupMenuItem(value: 'delete', child: Text('Delete')),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showAddContactDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddContactScreen()),
    );
  }

  void _showImportDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ImportContactsScreen()),
    );
  }

  void _showContactDetail(Recipient contact) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ContactDetailScreen(contact: contact),
      ),
    );
  }

  void _showGroupDetail(ContactGroup group) {
    // TODO: Implement group detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Group detail for ${group.name} - Coming soon')),
    );
  }

  void _showCreateGroupDialog() {
    // TODO: Implement create group dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create group - Coming soon')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        context.read<ContactProvider>().exportContacts();
        break;
      case 'bulk_delete':
        // TODO: Implement bulk delete
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Bulk delete - Coming soon')),
        );
        break;
      case 'settings':
        // TODO: Implement settings
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings - Coming soon')),
        );
        break;
    }
  }

  void _handleContactAction(String action, Recipient contact) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AddContactScreen(contact: contact),
          ),
        );
        break;
      case 'delete':
        _showDeleteContactDialog(contact);
        break;
      case 'add_to_group':
        // TODO: Implement add to group
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Add to group - Coming soon')),
        );
        break;
    }
  }

  void _handleGroupAction(String action, ContactGroup group) {
    switch (action) {
      case 'edit':
        // TODO: Implement edit group
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Edit group ${group.name} - Coming soon')),
        );
        break;
      case 'delete':
        _showDeleteGroupDialog(group);
        break;
      case 'view_contacts':
        // TODO: Implement view group contacts
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('View contacts in ${group.name} - Coming soon')),
        );
        break;
    }
  }

  void _handleTagAction(String action, String tag) {
    switch (action) {
      case 'rename':
        // TODO: Implement rename tag
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Rename tag $tag - Coming soon')),
        );
        break;
      case 'delete':
        // TODO: Implement delete tag
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Delete tag $tag - Coming soon')),
        );
        break;
    }
  }

  void _showDeleteContactDialog(Recipient contact) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Contact'),
        content: Text('Are you sure you want to delete ${contact.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ContactProvider>().deleteContact(contact.id!);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showDeleteGroupDialog(ContactGroup group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Group'),
        content: Text('Are you sure you want to delete the group "${group.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ContactProvider>().deleteGroup(group.id!);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
