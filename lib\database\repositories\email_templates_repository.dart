// lib/database/repositories/email_templates_repository.dart
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../database_helper.dart';
import '../../models/email_template_model.dart';

class EmailTemplatesRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  Future<String> create(EmailTemplate template) async {
    final db = await _dbHelper.database;
    final id = template.id ?? _uuid.v4();
    
    final templateWithId = template.copyWith(
      id: id,
      updatedAt: DateTime.now(),
    );

    await db.insert(
      'email_templates',
      {
        'id': templateWithId.id,
        'name': templateWithId.name,
        'subject': templateWithId.subject,
        'html_content': templateWithId.htmlContent,
        'plain_text_content': templateWithId.plainTextContent,
        'type': templateWithId.type.toString().split('.').last,
        'variables': jsonEncode(templateWithId.variables),
        'metadata': jsonEncode(templateWithId.metadata),
        'is_active': templateWithId.isActive ? 1 : 0,
        'created_at': templateWithId.createdAt.toIso8601String(),
        'updated_at': templateWithId.updatedAt?.toIso8601String(),
        'preview_image_url': templateWithId.previewImageUrl,
        'category': templateWithId.category,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    return id;
  }

  Future<EmailTemplate?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'email_templates',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToEmailTemplate(maps.first);
  }

  Future<List<EmailTemplate>> getAll({
    int? limit,
    int? offset,
    String? searchQuery,
    String? category,
    bool? isActive,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += 'name LIKE ? OR subject LIKE ?';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
    }

    if (category != null && category.isNotEmpty) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'category = ?';
      whereArgs.add(category);
    }

    if (isActive != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'is_active = ?';
      whereArgs.add(isActive ? 1 : 0);
    }

    final maps = await db.query(
      'email_templates',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map(_mapToEmailTemplate).toList();
  }

  Future<int> getCount({
    String? searchQuery,
    String? category,
    bool? isActive,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += 'name LIKE ? OR subject LIKE ?';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
    }

    if (category != null && category.isNotEmpty) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'category = ?';
      whereArgs.add(category);
    }

    if (isActive != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'is_active = ?';
      whereArgs.add(isActive ? 1 : 0);
    }

    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_templates${whereClause.isNotEmpty ? ' WHERE $whereClause' : ''}',
      whereArgs.isNotEmpty ? whereArgs : null,
    );

    return result.first['count'] as int;
  }

  Future<List<String>> getCategories() async {
    final db = await _dbHelper.database;
    final maps = await db.rawQuery(
      'SELECT DISTINCT category FROM email_templates WHERE category IS NOT NULL AND category != "" ORDER BY category',
    );

    return maps.map((map) => map['category'] as String).toList();
  }

  Future<void> update(EmailTemplate template) async {
    final db = await _dbHelper.database;
    
    final updatedTemplate = template.copyWith(updatedAt: DateTime.now());

    await db.update(
      'email_templates',
      {
        'name': updatedTemplate.name,
        'subject': updatedTemplate.subject,
        'html_content': updatedTemplate.htmlContent,
        'plain_text_content': updatedTemplate.plainTextContent,
        'type': updatedTemplate.type.toString().split('.').last,
        'variables': jsonEncode(updatedTemplate.variables),
        'metadata': jsonEncode(updatedTemplate.metadata),
        'is_active': updatedTemplate.isActive ? 1 : 0,
        'updated_at': updatedTemplate.updatedAt?.toIso8601String(),
        'preview_image_url': updatedTemplate.previewImageUrl,
        'category': updatedTemplate.category,
      },
      where: 'id = ?',
      whereArgs: [template.id],
    );
  }

  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'email_templates',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> setActive(String id, bool isActive) async {
    final db = await _dbHelper.database;
    await db.update(
      'email_templates',
      {
        'is_active': isActive ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<EmailTemplate?> duplicate(String id, String newName) async {
    final original = await getById(id);
    if (original == null) return null;

    final duplicated = original.copyWith(
      id: null,
      name: newName,
      createdAt: DateTime.now(),
      updatedAt: null,
    );

    final newId = await create(duplicated);
    return await getById(newId);
  }

  Future<List<EmailTemplate>> getByCategory(String category) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'email_templates',
      where: 'category = ? AND is_active = 1',
      whereArgs: [category],
      orderBy: 'name ASC',
    );

    return maps.map(_mapToEmailTemplate).toList();
  }

  Future<List<EmailTemplate>> getActive() async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'email_templates',
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return maps.map(_mapToEmailTemplate).toList();
  }

  EmailTemplate _mapToEmailTemplate(Map<String, dynamic> map) {
    return EmailTemplate(
      id: map['id'],
      name: map['name'],
      subject: map['subject'],
      htmlContent: map['html_content'],
      plainTextContent: map['plain_text_content'],
      type: TemplateType.values.firstWhere(
        (e) => e.toString() == 'TemplateType.${map['type']}',
        orElse: () => TemplateType.html,
      ),
      variables: _parseJsonList(map['variables']),
      metadata: _parseJson(map['metadata']),
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      previewImageUrl: map['preview_image_url'],
      category: map['category'],
    );
  }

  Map<String, dynamic> _parseJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return {};
    try {
      return Map<String, dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      return {};
    }
  }

  List<String> _parseJsonList(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return [];
    try {
      return List<String>.from(jsonDecode(jsonString));
    } catch (e) {
      return [];
    }
  }
}
