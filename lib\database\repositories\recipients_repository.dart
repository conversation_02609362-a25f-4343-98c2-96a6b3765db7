// lib/database/repositories/recipients_repository.dart
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../database_helper.dart';
import '../../models/recipient_model.dart';

class RecipientsRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  Future<String> create(Recipient recipient) async {
    final db = await _dbHelper.database;
    final id = recipient.id ?? _uuid.v4();
    
    final recipientWithId = recipient.copyWith(
      id: id,
      updatedAt: DateTime.now(),
    );

    await db.insert(
      'recipients',
      {
        'id': recipientWithId.id,
        'name': recipientWithId.name,
        'email': recipientWithId.email,
        'company': recipientWithId.company,
        'phone': recipientWithId.phone,
        'custom_fields': jsonEncode(recipientWithId.customFields),
        'tags': jsonEncode(recipientWithId.tags),
        'groups': jsonEncode(recipientWithId.groups),
        'is_subscribed': recipientWithId.isSubscribed ? 1 : 0,
        'created_at': recipientWithId.createdAt.toIso8601String(),
        'updated_at': recipientWithId.updatedAt?.toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    return id;
  }

  Future<List<String>> createBatch(List<Recipient> recipients) async {
    final db = await _dbHelper.database;
    final ids = <String>[];

    await db.transaction((txn) async {
      for (final recipient in recipients) {
        final id = recipient.id ?? _uuid.v4();
        ids.add(id);
        
        final recipientWithId = recipient.copyWith(
          id: id,
          updatedAt: DateTime.now(),
        );

        await txn.insert(
          'recipients',
          {
            'id': recipientWithId.id,
            'name': recipientWithId.name,
            'email': recipientWithId.email,
            'company': recipientWithId.company,
            'phone': recipientWithId.phone,
            'custom_fields': jsonEncode(recipientWithId.customFields),
            'tags': jsonEncode(recipientWithId.tags),
            'groups': jsonEncode(recipientWithId.groups),
            'is_subscribed': recipientWithId.isSubscribed ? 1 : 0,
            'created_at': recipientWithId.createdAt.toIso8601String(),
            'updated_at': recipientWithId.updatedAt?.toIso8601String(),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });

    return ids;
  }

  Future<Recipient?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'recipients',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToRecipient(maps.first);
  }

  Future<Recipient?> getByEmail(String email) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'recipients',
      where: 'email = ?',
      whereArgs: [email],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToRecipient(maps.first);
  }

  Future<List<Recipient>> getAll({
    int? limit,
    int? offset,
    String? searchQuery,
    List<String>? tags,
    List<String>? groups,
    bool? isSubscribed,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += 'name LIKE ? OR email LIKE ? OR company LIKE ?';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%', '%$searchQuery%']);
    }

    if (isSubscribed != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'is_subscribed = ?';
      whereArgs.add(isSubscribed ? 1 : 0);
    }

    final maps = await db.query(
      'recipients',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    List<Recipient> recipients = maps.map(_mapToRecipient).toList();

    // Filter by tags and groups if specified
    if (tags != null && tags.isNotEmpty) {
      recipients = recipients.where((r) => 
        tags.any((tag) => r.tags.contains(tag))
      ).toList();
    }

    if (groups != null && groups.isNotEmpty) {
      recipients = recipients.where((r) => 
        groups.any((group) => r.groups.contains(group))
      ).toList();
    }

    return recipients;
  }

  Future<int> getCount({
    String? searchQuery,
    List<String>? tags,
    List<String>? groups,
    bool? isSubscribed,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += 'name LIKE ? OR email LIKE ? OR company LIKE ?';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%', '%$searchQuery%']);
    }

    if (isSubscribed != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'is_subscribed = ?';
      whereArgs.add(isSubscribed ? 1 : 0);
    }

    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM recipients${whereClause.isNotEmpty ? ' WHERE $whereClause' : ''}',
      whereArgs.isNotEmpty ? whereArgs : null,
    );

    return result.first['count'] as int;
  }

  Future<void> update(Recipient recipient) async {
    final db = await _dbHelper.database;
    
    final updatedRecipient = recipient.copyWith(updatedAt: DateTime.now());

    await db.update(
      'recipients',
      {
        'name': updatedRecipient.name,
        'email': updatedRecipient.email,
        'company': updatedRecipient.company,
        'phone': updatedRecipient.phone,
        'custom_fields': jsonEncode(updatedRecipient.customFields),
        'tags': jsonEncode(updatedRecipient.tags),
        'groups': jsonEncode(updatedRecipient.groups),
        'is_subscribed': updatedRecipient.isSubscribed ? 1 : 0,
        'updated_at': updatedRecipient.updatedAt?.toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [recipient.id],
    );
  }

  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'recipients',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteBatch(List<String> ids) async {
    final db = await _dbHelper.database;
    await db.transaction((txn) async {
      for (final id in ids) {
        await txn.delete(
          'recipients',
          where: 'id = ?',
          whereArgs: [id],
        );
      }
    });
  }

  Future<void> unsubscribe(String email) async {
    final db = await _dbHelper.database;
    await db.update(
      'recipients',
      {
        'is_subscribed': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'email = ?',
      whereArgs: [email],
    );
  }

  Future<List<String>> getAllTags() async {
    final db = await _dbHelper.database;
    final maps = await db.query('recipients', columns: ['tags']);
    
    final Set<String> allTags = {};
    for (final map in maps) {
      final tagsJson = map['tags'] as String?;
      if (tagsJson != null && tagsJson.isNotEmpty) {
        try {
          final List<dynamic> tags = jsonDecode(tagsJson);
          allTags.addAll(tags.cast<String>());
        } catch (e) {
          // Ignore invalid JSON
        }
      }
    }
    
    return allTags.toList()..sort();
  }

  Future<List<String>> getAllGroups() async {
    final db = await _dbHelper.database;
    final maps = await db.query('recipients', columns: ['groups']);
    
    final Set<String> allGroups = {};
    for (final map in maps) {
      final groupsJson = map['groups'] as String?;
      if (groupsJson != null && groupsJson.isNotEmpty) {
        try {
          final List<dynamic> groups = jsonDecode(groupsJson);
          allGroups.addAll(groups.cast<String>());
        } catch (e) {
          // Ignore invalid JSON
        }
      }
    }
    
    return allGroups.toList()..sort();
  }

  Recipient _mapToRecipient(Map<String, dynamic> map) {
    return Recipient(
      id: map['id'],
      name: map['name'],
      email: map['email'],
      company: map['company'],
      phone: map['phone'],
      customFields: _parseJson(map['custom_fields']),
      tags: _parseJsonList(map['tags']),
      groups: _parseJsonList(map['groups']),
      isSubscribed: map['is_subscribed'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  Map<String, dynamic> _parseJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return {};
    try {
      return Map<String, dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      return {};
    }
  }

  List<String> _parseJsonList(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return [];
    try {
      return List<String>.from(jsonDecode(jsonString));
    } catch (e) {
      return [];
    }
  }
}
