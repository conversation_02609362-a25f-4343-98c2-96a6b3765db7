// lib/providers/auth_provider.dart
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/security_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final SecurityService _securityService = SecurityService();

  User? _currentUser;
  AuthSession? _currentSession;
  bool _isLoading = false;
  String? _error;

  // Getters
  User? get currentUser => _currentUser;
  AuthSession? get currentSession => _currentSession;
  bool get isAuthenticated => _authService.isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize the auth provider
  Future<void> initialize() async {
    _setLoading(true);
    try {
      final success = await _authService.initialize();
      if (success) {
        _currentUser = _authService.currentUser;
        _currentSession = _authService.currentSession;
        _logSecurityEvent('USER_SESSION_RESTORED', 'User session restored successfully');
      }
      _clearError();
    } catch (e) {
      _setError('Failed to initialize authentication: ${e.toString()}');
      _logSecurityEvent('AUTH_INIT_FAILED', 'Authentication initialization failed: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Register new user
  Future<bool> register({
    required String email,
    required String password,
    required String name,
    String? company,
    UserRole role = UserRole.user,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Validate input
      if (!_securityService.isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      final passwordStrength = _securityService.validatePasswordStrength(password);
      if (passwordStrength == PasswordStrength.weak) {
        _setError('Password is too weak. Please use at least 8 characters with mixed case, numbers, and symbols.');
        return false;
      }

      // Check rate limiting
      if (!_securityService.checkRateLimit('register_$email', maxAttempts: 3)) {
        _setError('Too many registration attempts. Please try again later.');
        _logSecurityEvent('RATE_LIMIT_EXCEEDED', 'Registration rate limit exceeded for email: $email');
        return false;
      }

      final result = await _authService.register(
        email: email,
        password: password,
        name: name,
        company: company,
        role: role,
      );

      if (result.success && result.user != null) {
        _currentUser = result.user;
        _currentSession = _authService.currentSession;
        _logSecurityEvent('USER_REGISTERED', 'New user registered: ${result.user!.email}');
        notifyListeners();
        return true;
      } else {
        _setError(result.error ?? 'Registration failed');
        _logSecurityEvent('REGISTRATION_FAILED', 'Registration failed for email: $email - ${result.error}');
        return false;
      }
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      _logSecurityEvent('REGISTRATION_ERROR', 'Registration error for email: $email - ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Login user
  Future<bool> login({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Validate input
      if (!_securityService.isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      // Check rate limiting
      if (!_securityService.checkRateLimit('login_$email', maxAttempts: 5)) {
        _setError('Too many login attempts. Please try again later.');
        _logSecurityEvent('RATE_LIMIT_EXCEEDED', 'Login rate limit exceeded for email: $email');
        return false;
      }

      final result = await _authService.login(
        email: email,
        password: password,
      );

      if (result.success && result.user != null) {
        _currentUser = result.user;
        _currentSession = _authService.currentSession;
        _securityService.clearRateLimit('login_$email'); // Clear rate limit on successful login
        _logSecurityEvent('USER_LOGIN', 'User logged in: ${result.user!.email}');
        notifyListeners();
        return true;
      } else {
        _setError(result.error ?? 'Login failed');
        _logSecurityEvent('LOGIN_FAILED', 'Login failed for email: $email - ${result.error}');
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      _logSecurityEvent('LOGIN_ERROR', 'Login error for email: $email - ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout user
  Future<void> logout() async {
    _setLoading(true);
    try {
      final userEmail = _currentUser?.email;
      await _authService.logout();
      _currentUser = null;
      _currentSession = null;
      _clearError();
      _logSecurityEvent('USER_LOGOUT', 'User logged out: $userEmail');
      notifyListeners();
    } catch (e) {
      _setError('Logout failed: ${e.toString()}');
      _logSecurityEvent('LOGOUT_ERROR', 'Logout error: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      if (_currentUser == null) {
        _setError('No user logged in');
        return false;
      }

      final passwordStrength = _securityService.validatePasswordStrength(newPassword);
      if (passwordStrength == PasswordStrength.weak) {
        _setError('New password is too weak. Please use at least 8 characters with mixed case, numbers, and symbols.');
        return false;
      }

      // Check rate limiting
      if (!_securityService.checkRateLimit('change_password_${_currentUser!.id}', maxAttempts: 3)) {
        _setError('Too many password change attempts. Please try again later.');
        _logSecurityEvent('RATE_LIMIT_EXCEEDED', 'Password change rate limit exceeded for user: ${_currentUser!.email}');
        return false;
      }

      final success = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (success) {
        _logSecurityEvent('PASSWORD_CHANGED', 'Password changed for user: ${_currentUser!.email}');
        return true;
      } else {
        _setError('Failed to change password. Please check your current password.');
        _logSecurityEvent('PASSWORD_CHANGE_FAILED', 'Password change failed for user: ${_currentUser!.email}');
        return false;
      }
    } catch (e) {
      _setError('Password change failed: ${e.toString()}');
      _logSecurityEvent('PASSWORD_CHANGE_ERROR', 'Password change error for user: ${_currentUser?.email} - ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      if (!_securityService.isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      // Check rate limiting
      if (!_securityService.checkRateLimit('reset_password_$email', maxAttempts: 3)) {
        _setError('Too many password reset attempts. Please try again later.');
        _logSecurityEvent('RATE_LIMIT_EXCEEDED', 'Password reset rate limit exceeded for email: $email');
        return false;
      }

      final success = await _authService.resetPassword(email);
      
      if (success) {
        _logSecurityEvent('PASSWORD_RESET', 'Password reset requested for email: $email');
        return true;
      } else {
        _setError('Failed to reset password. Please check the email address.');
        _logSecurityEvent('PASSWORD_RESET_FAILED', 'Password reset failed for email: $email');
        return false;
      }
    } catch (e) {
      _setError('Password reset failed: ${e.toString()}');
      _logSecurityEvent('PASSWORD_RESET_ERROR', 'Password reset error for email: $email - ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateProfile(User updatedUser) async {
    _setLoading(true);
    _clearError();

    try {
      if (_currentUser == null) {
        _setError('No user logged in');
        return false;
      }

      final success = await _authService.updateProfile(updatedUser);
      
      if (success) {
        _currentUser = updatedUser;
        _logSecurityEvent('PROFILE_UPDATED', 'Profile updated for user: ${updatedUser.email}');
        notifyListeners();
        return true;
      } else {
        _setError('Failed to update profile');
        _logSecurityEvent('PROFILE_UPDATE_FAILED', 'Profile update failed for user: ${updatedUser.email}');
        return false;
      }
    } catch (e) {
      _setError('Profile update failed: ${e.toString()}');
      _logSecurityEvent('PROFILE_UPDATE_ERROR', 'Profile update error for user: ${updatedUser.email} - ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check if user has permission
  bool hasPermission(String permission) {
    return _authService.hasPermission(permission);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void _logSecurityEvent(String type, String description) {
    _securityService.logSecurityEvent(SecurityEvent(
      type: type,
      description: description,
      userId: _currentUser?.id,
    ));
  }
}
