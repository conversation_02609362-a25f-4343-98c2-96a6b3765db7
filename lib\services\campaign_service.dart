// lib/services/campaign_service.dart
import 'dart:async';
import '../models/campaign_model.dart';
import '../models/recipient_model.dart';
import '../models/email_template_model.dart';
import '../database/repositories/campaigns_repository.dart';
import '../database/repositories/recipients_repository.dart';
import '../database/repositories/email_templates_repository.dart';
import 'email_service.dart';
import 'template_service.dart';

class CampaignService {
  static final CampaignService _instance = CampaignService._internal();
  factory CampaignService() => _instance;
  CampaignService._internal();

  final CampaignsRepository _campaignRepository = CampaignsRepository();
  final RecipientsRepository _recipientsRepository = RecipientsRepository();
  final EmailTemplatesRepository _templatesRepository = EmailTemplatesRepository();
  final EmailService _emailService = EmailService();
  final TemplateService _templateService = TemplateService();

  final Map<String, Timer> _scheduledCampaigns = {};
  final Map<String, StreamController<CampaignProgress>> _campaignProgressControllers = {};

  // Create campaign
  Future<String> createCampaign(Campaign campaign) async {
    try {
      // Validate campaign
      final validation = await _validateCampaign(campaign);
      if (!validation.isValid) {
        throw CampaignException('Campaign validation failed: ${validation.errors.join(', ')}');
      }

      final id = await _campaignRepository.create(campaign);
      
      // Schedule campaign if it has a scheduled time
      if (campaign.scheduledAt != null && campaign.status == CampaignStatus.scheduled) {
        await _scheduleCampaign(id, campaign.scheduledAt!);
      }

      return id;
    } catch (e) {
      throw CampaignException('Failed to create campaign: ${e.toString()}');
    }
  }

  // Get campaign
  Future<Campaign?> getCampaign(String id) async {
    try {
      return await _campaignRepository.getById(id);
    } catch (e) {
      throw CampaignException('Failed to get campaign: ${e.toString()}');
    }
  }

  // Get campaigns with filtering
  Future<List<Campaign>> getCampaigns({
    int? limit,
    int? offset,
    String? searchQuery,
    CampaignStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _campaignRepository.getAll(
        limit: limit,
        offset: offset,
        searchQuery: searchQuery,
        status: status,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      throw CampaignException('Failed to get campaigns: ${e.toString()}');
    }
  }

  // Update campaign
  Future<void> updateCampaign(Campaign campaign) async {
    try {
      await _campaignRepository.update(campaign);
      
      // Handle scheduling changes
      if (campaign.scheduledAt != null && campaign.status == CampaignStatus.scheduled) {
        await _scheduleCampaign(campaign.id!, campaign.scheduledAt!);
      } else if (_scheduledCampaigns.containsKey(campaign.id)) {
        _cancelScheduledCampaign(campaign.id!);
      }
    } catch (e) {
      throw CampaignException('Failed to update campaign: ${e.toString()}');
    }
  }

  // Delete campaign
  Future<void> deleteCampaign(String id) async {
    try {
      _cancelScheduledCampaign(id);
      await _campaignRepository.delete(id);
    } catch (e) {
      throw CampaignException('Failed to delete campaign: ${e.toString()}');
    }
  }

  // Send campaign immediately
  Future<void> sendCampaign(String campaignId) async {
    try {
      final campaign = await getCampaign(campaignId);
      if (campaign == null) {
        throw CampaignException('Campaign not found');
      }

      if (campaign.status != CampaignStatus.draft && campaign.status != CampaignStatus.scheduled) {
        throw CampaignException('Campaign cannot be sent in current status: ${campaign.status}');
      }

      // Update campaign status to sending
      await _updateCampaignStatus(campaignId, CampaignStatus.sending);

      // Execute campaign
      await _executeCampaign(campaign);
    } catch (e) {
      await _updateCampaignStatus(campaignId, CampaignStatus.failed);
      throw CampaignException('Failed to send campaign: ${e.toString()}');
    }
  }

  // Schedule campaign
  Future<void> scheduleCampaign(String campaignId, DateTime scheduledTime) async {
    try {
      final campaign = await getCampaign(campaignId);
      if (campaign == null) {
        throw CampaignException('Campaign not found');
      }

      final updatedCampaign = campaign.copyWith(
        scheduledAt: scheduledTime,
        status: CampaignStatus.scheduled,
      );

      await updateCampaign(updatedCampaign);
    } catch (e) {
      throw CampaignException('Failed to schedule campaign: ${e.toString()}');
    }
  }

  // Cancel campaign
  Future<void> cancelCampaign(String campaignId) async {
    try {
      _cancelScheduledCampaign(campaignId);
      await _updateCampaignStatus(campaignId, CampaignStatus.cancelled);
    } catch (e) {
      throw CampaignException('Failed to cancel campaign: ${e.toString()}');
    }
  }

  // Pause campaign
  Future<void> pauseCampaign(String campaignId) async {
    try {
      await _updateCampaignStatus(campaignId, CampaignStatus.paused);
    } catch (e) {
      throw CampaignException('Failed to pause campaign: ${e.toString()}');
    }
  }

  // Resume campaign
  Future<void> resumeCampaign(String campaignId) async {
    try {
      await _updateCampaignStatus(campaignId, CampaignStatus.sending);
    } catch (e) {
      throw CampaignException('Failed to resume campaign: ${e.toString()}');
    }
  }

  // Get campaign progress stream
  Stream<CampaignProgress> getCampaignProgress(String campaignId) {
    if (!_campaignProgressControllers.containsKey(campaignId)) {
      _campaignProgressControllers[campaignId] = StreamController<CampaignProgress>.broadcast();
    }
    return _campaignProgressControllers[campaignId]!.stream;
  }

  // Get campaign statistics
  Future<CampaignStats> getCampaignStats() async {
    try {
      final allCampaigns = await _campaignRepository.getAll();
      
      final stats = <CampaignStatus, int>{};
      for (final status in CampaignStatus.values) {
        stats[status] = allCampaigns.where((c) => c.status == status).length;
      }

      final totalSent = allCampaigns.fold<int>(0, (sum, c) => sum + (c.analytics.emailsSent ?? 0));
      final totalDelivered = allCampaigns.fold<int>(0, (sum, c) => sum + (c.analytics.emailsDelivered ?? 0));
      final totalOpened = allCampaigns.fold<int>(0, (sum, c) => sum + (c.analytics.emailsOpened ?? 0));
      final totalClicked = allCampaigns.fold<int>(0, (sum, c) => sum + (c.analytics.emailsClicked ?? 0));

      return CampaignStats(
        totalCampaigns: allCampaigns.length,
        statusStats: stats,
        totalEmailsSent: totalSent,
        totalEmailsDelivered: totalDelivered,
        totalEmailsOpened: totalOpened,
        totalEmailsClicked: totalClicked,
        averageOpenRate: totalSent > 0 ? (totalOpened / totalSent) * 100 : 0,
        averageClickRate: totalSent > 0 ? (totalClicked / totalSent) * 100 : 0,
        averageDeliveryRate: totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0,
      );
    } catch (e) {
      throw CampaignException('Failed to get campaign stats: ${e.toString()}');
    }
  }

  // Private methods
  Future<void> _executeCampaign(Campaign campaign) async {
    try {
      // Get template
      final template = await _templatesRepository.getById(campaign.templateId);
      if (template == null) {
        throw CampaignException('Template not found');
      }

      // Get recipients
      final recipients = await _getRecipients(campaign);
      if (recipients.isEmpty) {
        throw CampaignException('No recipients found for campaign');
      }

      // Initialize progress tracking
      final progressController = _campaignProgressControllers[campaign.id!];
      var processedCount = 0;
      var successCount = 0;
      var failureCount = 0;

      // Send emails in batches
      const batchSize = 50;
      for (int i = 0; i < recipients.length; i += batchSize) {
        final batch = recipients.skip(i).take(batchSize).toList();
        
        for (final recipient in batch) {
          try {
            // Render template for recipient
            final rendered = _templateService.renderTemplate(template, recipient);
            
            // Send email
            final result = await _emailService.sendEmail(
              to: recipient.email,
              toName: recipient.name,
              subject: rendered.subject,
              htmlContent: rendered.htmlContent,
              plainTextContent: rendered.plainTextContent,
              campaignId: campaign.id,
              templateId: template.id,
            );

            if (result.success) {
              successCount++;
            } else {
              failureCount++;
            }
          } catch (e) {
            failureCount++;
          }

          processedCount++;

          // Update progress
          progressController?.add(CampaignProgress(
            campaignId: campaign.id!,
            totalRecipients: recipients.length,
            processedCount: processedCount,
            successCount: successCount,
            failureCount: failureCount,
            isComplete: processedCount == recipients.length,
          ));
        }

        // Small delay between batches
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Update campaign status and analytics
      final updatedAnalytics = campaign.analytics.copyWith(
        emailsSent: successCount,
        emailsFailed: failureCount,
        lastSentAt: DateTime.now(),
      );

      final updatedCampaign = campaign.copyWith(
        status: CampaignStatus.completed,
        analytics: updatedAnalytics,
        sentAt: DateTime.now(),
      );

      await _campaignRepository.update(updatedCampaign);

      // Close progress controller
      progressController?.close();
      _campaignProgressControllers.remove(campaign.id!);
    } catch (e) {
      await _updateCampaignStatus(campaign.id!, CampaignStatus.failed);
      rethrow;
    }
  }

  Future<List<Recipient>> _getRecipients(Campaign campaign) async {
    // Get recipients based on campaign settings
    return await _recipientsRepository.getAll(
      tags: campaign.settings.targetTags,
      groups: campaign.settings.targetGroups,
      isSubscribed: true, // Only send to subscribed recipients
    );
  }

  Future<void> _scheduleCampaign(String campaignId, DateTime scheduledTime) async {
    // Cancel existing timer if any
    _cancelScheduledCampaign(campaignId);

    final now = DateTime.now();
    if (scheduledTime.isBefore(now)) {
      throw CampaignException('Cannot schedule campaign in the past');
    }

    final delay = scheduledTime.difference(now);
    _scheduledCampaigns[campaignId] = Timer(delay, () async {
      try {
        await sendCampaign(campaignId);
      } catch (e) {
        print('Failed to send scheduled campaign $campaignId: $e');
      }
      _scheduledCampaigns.remove(campaignId);
    });
  }

  void _cancelScheduledCampaign(String campaignId) {
    final timer = _scheduledCampaigns.remove(campaignId);
    timer?.cancel();
  }

  Future<void> _updateCampaignStatus(String campaignId, CampaignStatus status) async {
    final campaign = await getCampaign(campaignId);
    if (campaign != null) {
      final updatedCampaign = campaign.copyWith(status: status);
      await _campaignRepository.update(updatedCampaign);
    }
  }

  Future<CampaignValidationResult> _validateCampaign(Campaign campaign) async {
    final errors = <String>[];
    final warnings = <String>[];

    // Check required fields
    if (campaign.name.trim().isEmpty) {
      errors.add('Campaign name is required');
    }

    if (campaign.templateId.isEmpty) {
      errors.add('Template is required');
    }

    // Check template exists
    final template = await _templatesRepository.getById(campaign.templateId);
    if (template == null) {
      errors.add('Selected template does not exist');
    }

    // Check recipients
    final recipients = await _getRecipients(campaign);
    if (recipients.isEmpty) {
      errors.add('No recipients found for this campaign');
    } else if (recipients.length > 10000) {
      warnings.add('Large recipient list (${recipients.length}) - consider segmentation');
    }

    // Check scheduling
    if (campaign.scheduledAt != null && campaign.scheduledAt!.isBefore(DateTime.now())) {
      errors.add('Scheduled time cannot be in the past');
    }

    return CampaignValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      recipientCount: recipients.length,
    );
  }
}

class CampaignProgress {
  final String campaignId;
  final int totalRecipients;
  final int processedCount;
  final int successCount;
  final int failureCount;
  final bool isComplete;

  CampaignProgress({
    required this.campaignId,
    required this.totalRecipients,
    required this.processedCount,
    required this.successCount,
    required this.failureCount,
    required this.isComplete,
  });

  double get progressPercentage => totalRecipients > 0 ? (processedCount / totalRecipients) * 100 : 0;
  double get successRate => processedCount > 0 ? (successCount / processedCount) * 100 : 0;
}

class CampaignValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final int recipientCount;

  CampaignValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.recipientCount,
  });
}

class CampaignStats {
  final int totalCampaigns;
  final Map<CampaignStatus, int> statusStats;
  final int totalEmailsSent;
  final int totalEmailsDelivered;
  final int totalEmailsOpened;
  final int totalEmailsClicked;
  final double averageOpenRate;
  final double averageClickRate;
  final double averageDeliveryRate;

  CampaignStats({
    required this.totalCampaigns,
    required this.statusStats,
    required this.totalEmailsSent,
    required this.totalEmailsDelivered,
    required this.totalEmailsOpened,
    required this.totalEmailsClicked,
    required this.averageOpenRate,
    required this.averageClickRate,
    required this.averageDeliveryRate,
  });
}

class CampaignException implements Exception {
  final String message;
  CampaignException(this.message);
  
  @override
  String toString() => 'CampaignException: $message';
}
