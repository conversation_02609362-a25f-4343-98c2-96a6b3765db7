// test/integration_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import '../lib/app.dart';
import '../lib/services/database_service.dart';
import '../lib/services/auth_service.dart';
import '../lib/services/email_service.dart';
import '../lib/services/analytics_service.dart';
import '../lib/services/compliance_service.dart';
import '../lib/models/contact.dart';
import '../lib/models/email_template.dart';
import '../lib/models/email_campaign.dart';
import '../lib/models/user.dart';

void main() {
  group('Email Marketing Application Integration Tests', () {
    late DatabaseService databaseService;
    late AuthService authService;
    late EmailService emailService;
    late AnalyticsService analyticsService;
    late ComplianceService complianceService;

    setUpAll(() async {
      // Initialize services
      databaseService = DatabaseService();
      await databaseService.initDatabase();
      
      authService = AuthService();
      emailService = EmailService();
      analyticsService = AnalyticsService();
      complianceService = ComplianceService();
    });

    tearDownAll(() async {
      // Clean up
      await databaseService.closeDatabase();
    });

    group('Authentication Tests', () {
      testWidgets('Login with valid credentials', (WidgetTester tester) async {
        await tester.pumpWidget(const PdfMailerApp());
        await tester.pumpAndSettle();

        // Find login form elements
        expect(find.byType(TextField), findsAtLeast(2));
        expect(find.text('Login'), findsOneWidget);

        // Enter credentials
        await tester.enterText(find.byType(TextField).first, '<EMAIL>');
        await tester.enterText(find.byType(TextField).last, 'Admin123!');

        // Tap login button
        await tester.tap(find.widgetWithText(ElevatedButton, 'Login'));
        await tester.pumpAndSettle();

        // Should navigate to dashboard
        expect(find.text('Dashboard'), findsOneWidget);
      });

      test('User registration and authentication', () async {
        final user = User(
          id: 'test_user_1',
          name: 'Test User',
          email: '<EMAIL>',
          role: UserRole.user,
          createdAt: DateTime.now(),
          isActive: true,
        );

        // Register user
        final result = await authService.register(
          user.email,
          'TestPassword123!',
          user.name,
          user.role,
        );
        expect(result.isSuccess, true);

        // Login with new user
        final loginResult = await authService.login(user.email, 'TestPassword123!');
        expect(loginResult.isSuccess, true);
        expect(loginResult.user?.email, user.email);
      });
    });

    group('Contact Management Tests', () {
      test('Create, read, update, delete contact', () async {
        final contact = Contact(
          id: 'test_contact_1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          company: 'Test Company',
          isSubscribed: true,
          createdAt: DateTime.now(),
          customFields: {'department': 'IT', 'position': 'Developer'},
          tags: ['developer', 'tech'],
          groups: ['newsletter'],
        );

        // Create contact
        final contactRepository = databaseService.contactRepository;
        await contactRepository.createContact(contact);

        // Read contact
        final retrievedContact = await contactRepository.getContactById(contact.id);
        expect(retrievedContact?.email, contact.email);
        expect(retrievedContact?.name, contact.name);

        // Update contact
        final updatedContact = contact.copyWith(
          name: 'John Smith',
          company: 'Updated Company',
        );
        await contactRepository.updateContact(updatedContact);

        final updatedRetrieved = await contactRepository.getContactById(contact.id);
        expect(updatedRetrieved?.name, 'John Smith');
        expect(updatedRetrieved?.company, 'Updated Company');

        // Delete contact
        await contactRepository.deleteContact(contact.id);
        final deletedContact = await contactRepository.getContactById(contact.id);
        expect(deletedContact, null);
      });

      test('Contact import and validation', () async {
        final csvData = '''
Name,Email,Phone,Company
Alice Johnson,<EMAIL>,+1111111111,Company A
Bob Wilson,<EMAIL>,+2222222222,Company B
Charlie Brown,<EMAIL>,+3333333333,Company C
''';

        // Test CSV parsing and validation
        final contacts = _parseCSVContacts(csvData);
        expect(contacts.length, 3);
        expect(contacts[0].name, 'Alice Johnson');
        expect(contacts[1].email, '<EMAIL>');
        expect(contacts[2].company, 'Company C');

        // Test email validation
        for (final contact in contacts) {
          expect(_isValidEmail(contact.email), true);
        }
      });
    });

    group('Email Template Tests', () {
      test('Create and manage email templates', () async {
        final template = EmailTemplate(
          id: 'test_template_1',
          name: 'Welcome Email',
          subject: 'Welcome to {{company_name}}!',
          htmlContent: '<h1>Welcome {{name}}!</h1><p>Thank you for joining {{company_name}}.</p>',
          plainTextContent: 'Welcome {{name}}! Thank you for joining {{company_name}}.',
          type: TemplateType.html,
          category: 'welcome',
          createdAt: DateTime.now(),
          variables: ['name', 'company_name'],
          isActive: true,
        );

        final templateRepository = databaseService.templateRepository;
        await templateRepository.createTemplate(template);

        // Test template retrieval
        final retrieved = await templateRepository.getTemplateById(template.id);
        expect(retrieved?.name, template.name);
        expect(retrieved?.variables.length, 2);

        // Test variable substitution
        final substituted = _substituteVariables(
          template.htmlContent,
          {'name': 'John Doe', 'company_name': 'Acme Corp'},
        );
        expect(substituted, '<h1>Welcome John Doe!</h1><p>Thank you for joining Acme Corp.</p>');
      });
    });

    group('Campaign Management Tests', () {
      test('Create and execute email campaign', () async {
        // Create test contacts
        final contacts = [
          Contact(
            id: 'contact_1',
            name: 'Test User 1',
            email: '<EMAIL>',
            isSubscribed: true,
            createdAt: DateTime.now(),
          ),
          Contact(
            id: 'contact_2',
            name: 'Test User 2',
            email: '<EMAIL>',
            isSubscribed: true,
            createdAt: DateTime.now(),
          ),
        ];

        final contactRepository = databaseService.contactRepository;
        for (final contact in contacts) {
          await contactRepository.createContact(contact);
        }

        // Create campaign
        final campaign = EmailCampaign(
          id: 'test_campaign_1',
          name: 'Test Campaign',
          subject: 'Test Subject',
          content: 'Hello {{name}}, this is a test email.',
          senderName: 'Test Sender',
          senderEmail: '<EMAIL>',
          recipientIds: contacts.map((c) => c.id).toList(),
          status: CampaignStatus.draft,
          createdAt: DateTime.now(),
          templateId: null,
        );

        final campaignRepository = databaseService.campaignRepository;
        await campaignRepository.createCampaign(campaign);

        // Test campaign execution (mock)
        final executedCampaign = campaign.copyWith(
          status: CampaignStatus.sent,
          sentAt: DateTime.now(),
        );
        await campaignRepository.updateCampaign(executedCampaign);

        final retrieved = await campaignRepository.getCampaignById(campaign.id);
        expect(retrieved?.status, CampaignStatus.sent);
        expect(retrieved?.sentAt, isNotNull);
      });
    });

    group('Analytics Tests', () {
      test('Generate analytics and reports', () async {
        // Test analytics data generation
        final overview = await analyticsService.getOverviewMetrics('30d');
        expect(overview, isA<Map<String, dynamic>>());
        expect(overview.containsKey('totalCampaigns'), true);
        expect(overview.containsKey('totalEmailsSent'), true);

        // Test trend calculation
        final trends = await analyticsService.getTrends('30d');
        expect(trends, isA<Map<String, dynamic>>());
        expect(trends.containsKey('campaignsTrend'), true);

        // Test report export
        try {
          await analyticsService.exportReport('json', '30d');
          // Should not throw exception
        } catch (e) {
          fail('Report export failed: $e');
        }
      });
    });

    group('Compliance Tests', () {
      test('GDPR compliance features', () async {
        final contactId = 'compliance_test_contact';
        
        // Test consent recording
        await complianceService.recordConsent(
          contactId,
          ConsentType.marketing,
          'website_signup',
        );

        // Test consent validation
        final hasConsent = await complianceService.hasValidConsent(
          contactId,
          ConsentType.marketing,
        );
        expect(hasConsent, true);

        // Test consent withdrawal
        await complianceService.withdrawConsent(
          contactId,
          ConsentType.marketing,
          'user_request',
        );

        final hasConsentAfterWithdrawal = await complianceService.hasValidConsent(
          contactId,
          ConsentType.marketing,
        );
        expect(hasConsentAfterWithdrawal, false);
      });

      test('CAN-SPAM compliance validation', () async {
        final compliantContent = '''
        <h1>Newsletter</h1>
        <p>This is our monthly newsletter.</p>
        <p>You can unsubscribe at any time by clicking <a href="{{unsubscribe_link}}">here</a>.</p>
        <p>Our address: 123 Main St, Anytown, ST 12345</p>
        ''';

        final result = await complianceService.validateEmailContent(
          'Monthly Newsletter',
          compliantContent,
          '<EMAIL>',
        );

        expect(result.isCompliant, true);
        expect(result.violations.isEmpty, true);
      });
    });

    group('UI Navigation Tests', () {
      testWidgets('Navigate through all main screens', (WidgetTester tester) async {
        await tester.pumpWidget(const PdfMailerApp());
        await tester.pumpAndSettle();

        // Login first
        await tester.enterText(find.byType(TextField).first, '<EMAIL>');
        await tester.enterText(find.byType(TextField).last, 'Admin123!');
        await tester.tap(find.widgetWithText(ElevatedButton, 'Login'));
        await tester.pumpAndSettle();

        // Test navigation to Contacts
        await tester.tap(find.text('Contacts'));
        await tester.pumpAndSettle();
        expect(find.text('Contacts'), findsOneWidget);

        // Test navigation to Templates
        await tester.tap(find.text('Templates'));
        await tester.pumpAndSettle();
        expect(find.text('Email Templates'), findsOneWidget);

        // Test navigation to Campaigns
        await tester.tap(find.text('Campaigns'));
        await tester.pumpAndSettle();
        expect(find.text('Campaigns'), findsOneWidget);

        // Test navigation to Analytics
        await tester.tap(find.text('Analytics'));
        await tester.pumpAndSettle();
        expect(find.text('Analytics & Reports'), findsOneWidget);
      });
    });
  });
}

// Helper functions for testing

List<Contact> _parseCSVContacts(String csvData) {
  final lines = csvData.trim().split('\n');
  final headers = lines[0].split(',');
  final contacts = <Contact>[];

  for (int i = 1; i < lines.length; i++) {
    final values = lines[i].split(',');
    final contact = Contact(
      id: 'csv_${DateTime.now().millisecondsSinceEpoch}_$i',
      name: values[0],
      email: values[1],
      phone: values[2],
      company: values[3],
      isSubscribed: true,
      createdAt: DateTime.now(),
    );
    contacts.add(contact);
  }

  return contacts;
}

bool _isValidEmail(String email) {
  return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
}

String _substituteVariables(String content, Map<String, String> variables) {
  String result = content;
  variables.forEach((key, value) {
    result = result.replaceAll('{{$key}}', value);
  });
  return result;
}
