// lib/models/recipient_model.dart
class Recipient {
  final String? id;
  final String name;
  final String email;
  final String? company;
  final String? phone;
  final Map<String, dynamic> customFields;
  final List<String> tags;
  final List<String> groups;
  final bool isSubscribed;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Recipient({
    this.id,
    required this.name,
    required this.email,
    this.company,
    this.phone,
    this.customFields = const {},
    this.tags = const [],
    this.groups = const [],
    this.isSubscribed = true,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Recipient.fromCsv(List<dynamic> fields) {
    if (fields.length < 2 || fields[0] == null || fields[1] == null) {
      throw FormatException("Invalid CSV row: $fields");
    }

    final name = fields[0].toString().trim();
    final email = fields[1].toString().trim();

    if (name.isEmpty || email.isEmpty || !email.contains('@')) {
      throw FormatException("Invalid name/email: $fields");
    }

    // Extended CSV parsing for additional fields
    final company = fields.length > 2 ? fields[2]?.toString().trim() : null;
    final phone = fields.length > 3 ? fields[3]?.toString().trim() : null;

    return Recipient(name: name, email: email, company: company, phone: phone);
  }

  factory Recipient.fromJson(Map<String, dynamic> json) {
    return Recipient(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      company: json['company'],
      phone: json['phone'],
      customFields: Map<String, dynamic>.from(json['customFields'] ?? {}),
      tags: List<String>.from(json['tags'] ?? []),
      groups: List<String>.from(json['groups'] ?? []),
      isSubscribed: json['isSubscribed'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'company': company,
      'phone': phone,
      'customFields': customFields,
      'tags': tags,
      'groups': groups,
      'isSubscribed': isSubscribed,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  Recipient copyWith({
    String? id,
    String? name,
    String? email,
    String? company,
    String? phone,
    Map<String, dynamic>? customFields,
    List<String>? tags,
    List<String>? groups,
    bool? isSubscribed,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipient(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      company: company ?? this.company,
      phone: phone ?? this.phone,
      customFields: customFields ?? this.customFields,
      tags: tags ?? this.tags,
      groups: groups ?? this.groups,
      isSubscribed: isSubscribed ?? this.isSubscribed,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
