// lib/models/recipient_model.dart
class Recipient {
  final String? id;
  final String name;
  final String email;
  final String? company;
  final String? position;
  final String? phone;
  final String? website;
  final String? address;
  final String? notes;
  final Map<String, dynamic> customFields;
  final List<String> tags;
  final List<String> groups;
  final bool isSubscribed;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Recipient({
    this.id,
    required this.name,
    required this.email,
    this.company,
    this.position,
    this.phone,
    this.website,
    this.address,
    this.notes,
    this.customFields = const {},
    this.tags = const [],
    this.groups = const [],
    this.isSubscribed = true,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Recipient.fromCsv(List<dynamic> fields) {
    if (fields.length < 2 || fields[0] == null || fields[1] == null) {
      throw FormatException("Invalid CSV row: $fields");
    }

    final name = fields[0].toString().trim();
    final email = fields[1].toString().trim();

    if (name.isEmpty || email.isEmpty || !email.contains('@')) {
      throw FormatException("Invalid name/email: $fields");
    }

    // Extended CSV parsing for additional fields
    final company = fields.length > 2 ? fields[2]?.toString().trim() : null;
    final phone = fields.length > 3 ? fields[3]?.toString().trim() : null;

    return Recipient(name: name, email: email, company: company, phone: phone);
  }

  factory Recipient.fromJson(Map<String, dynamic> json) {
    return Recipient(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      company: json['company'],
      position: json['position'],
      phone: json['phone'],
      website: json['website'],
      address: json['address'],
      notes: json['notes'],
      customFields: Map<String, dynamic>.from(json['customFields'] ?? {}),
      tags: List<String>.from(json['tags'] ?? []),
      groups: List<String>.from(json['groups'] ?? []),
      isSubscribed: json['isSubscribed'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'company': company,
      'position': position,
      'phone': phone,
      'website': website,
      'address': address,
      'notes': notes,
      'customFields': customFields,
      'tags': tags,
      'groups': groups,
      'isSubscribed': isSubscribed,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  Recipient copyWith({
    String? id,
    String? name,
    String? email,
    String? company,
    String? position,
    String? phone,
    String? website,
    String? address,
    String? notes,
    Map<String, dynamic>? customFields,
    List<String>? tags,
    List<String>? groups,
    bool? isSubscribed,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipient(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      company: company ?? this.company,
      position: position ?? this.position,
      phone: phone ?? this.phone,
      website: website ?? this.website,
      address: address ?? this.address,
      notes: notes ?? this.notes,
      customFields: customFields ?? this.customFields,
      tags: tags ?? this.tags,
      groups: groups ?? this.groups,
      isSubscribed: isSubscribed ?? this.isSubscribed,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
