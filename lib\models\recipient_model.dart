// lib/models/recipient_model.dart
class Recipient {
  final String name;
  final String email;

  Recipient({required this.name, required this.email});

  factory Recipient.fromCsv(List<dynamic> fields) {
    if (fields.length < 2 || fields[0] == null || fields[1] == null) {
      throw FormatException("Invalid CSV row: $fields");
    }

    final name = fields[0].toString().trim();
    final email = fields[1].toString().trim();

    if (name.isEmpty || email.isEmpty || !email.contains('@')) {
      throw FormatException("Invalid name/email: $fields");
    }

    return Recipient(name: name, email: email);
  }
}
