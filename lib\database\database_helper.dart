// lib/database/database_helper.dart
import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/recipient_model.dart';
import '../models/email_template_model.dart';
import '../models/campaign_model.dart';
import '../models/email_tracking_model.dart';
import '../models/user_model.dart';
import '../models/email_service_config_model.dart';
import '../models/contact_group_model.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'email_marketing.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        company TEXT,
        role TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        last_login_at TEXT,
        preferences TEXT,
        permissions TEXT,
        avatar_url TEXT,
        phone TEXT,
        timezone TEXT
      )
    ''');

    // User passwords table (separate for security)
    await db.execute('''
      CREATE TABLE user_passwords (
        user_id TEXT PRIMARY KEY,
        password_hash TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Auth sessions table
    await db.execute('''
      CREATE TABLE auth_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        token TEXT NOT NULL,
        created_at TEXT NOT NULL,
        expires_at TEXT NOT NULL,
        device_info TEXT,
        ip_address TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Recipients/Contacts table
    await db.execute('''
      CREATE TABLE recipients (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        company TEXT,
        phone TEXT,
        custom_fields TEXT,
        tags TEXT,
        groups TEXT,
        is_subscribed INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Contact groups table
    await db.execute('''
      CREATE TABLE contact_groups (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL,
        contact_ids TEXT,
        criteria TEXT,
        contact_count INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        color TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        metadata TEXT
      )
    ''');

    // Contact tags table
    await db.execute('''
      CREATE TABLE contact_tags (
        id TEXT PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        color TEXT,
        contact_count INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_active INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // Email templates table
    await db.execute('''
      CREATE TABLE email_templates (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        subject TEXT NOT NULL,
        html_content TEXT NOT NULL,
        plain_text_content TEXT,
        type TEXT NOT NULL,
        variables TEXT,
        metadata TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        preview_image_url TEXT,
        category TEXT
      )
    ''');

    // Email service configurations table
    await db.execute('''
      CREATE TABLE email_service_configs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        provider TEXT NOT NULL,
        credentials TEXT NOT NULL,
        settings TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        daily_limit INTEGER,
        monthly_limit INTEGER,
        rate_limit INTEGER
      )
    ''');

    // Campaigns table
    await db.execute('''
      CREATE TABLE campaigns (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        template_id TEXT NOT NULL,
        recipient_groups TEXT,
        recipient_tags TEXT,
        specific_recipients TEXT,
        scheduled_at TEXT,
        sent_at TEXT,
        settings TEXT,
        analytics TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        from_name TEXT,
        from_email TEXT,
        reply_to_email TEXT,
        FOREIGN KEY (template_id) REFERENCES email_templates (id)
      )
    ''');

    // Email tracking table
    await db.execute('''
      CREATE TABLE email_tracking (
        id TEXT PRIMARY KEY,
        campaign_id TEXT NOT NULL,
        recipient_id TEXT NOT NULL,
        recipient_email TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        sent_at TEXT,
        delivered_at TEXT,
        opened_at TEXT,
        clicked_at TEXT,
        bounced_at TEXT,
        failed_at TEXT,
        error_message TEXT,
        bounce_type TEXT,
        bounce_reason TEXT,
        metadata TEXT,
        events TEXT,
        FOREIGN KEY (campaign_id) REFERENCES campaigns (id) ON DELETE CASCADE,
        FOREIGN KEY (recipient_id) REFERENCES recipients (id)
      )
    ''');

    // Email service usage table
    await db.execute('''
      CREATE TABLE email_service_usage (
        id TEXT PRIMARY KEY,
        service_config_id TEXT NOT NULL,
        date TEXT NOT NULL,
        emails_sent INTEGER DEFAULT 0,
        emails_delivered INTEGER DEFAULT 0,
        emails_bounced INTEGER DEFAULT 0,
        emails_failed INTEGER DEFAULT 0,
        cost REAL DEFAULT 0.0,
        FOREIGN KEY (service_config_id) REFERENCES email_service_configs (id),
        UNIQUE(service_config_id, date)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_recipients_email ON recipients (email)');
    await db.execute(
      'CREATE INDEX idx_recipients_subscribed ON recipients (is_subscribed)',
    );
    await db.execute('CREATE INDEX idx_campaigns_status ON campaigns (status)');
    await db.execute(
      'CREATE INDEX idx_campaigns_scheduled ON campaigns (scheduled_at)',
    );
    await db.execute(
      'CREATE INDEX idx_email_tracking_campaign ON email_tracking (campaign_id)',
    );
    await db.execute(
      'CREATE INDEX idx_email_tracking_status ON email_tracking (status)',
    );
    await db.execute(
      'CREATE INDEX idx_auth_sessions_user ON auth_sessions (user_id)',
    );
    await db.execute(
      'CREATE INDEX idx_auth_sessions_token ON auth_sessions (token)',
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database schema upgrades here
    // For now, we'll just recreate the database
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  // Helper methods for JSON serialization
  String _encodeJson(dynamic data) {
    return data != null ? jsonEncode(data) : '{}';
  }

  T _decodeJson<T>(
    String? jsonString,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (jsonString == null || jsonString.isEmpty) {
      return fromJson({});
    }
    try {
      return fromJson(jsonDecode(jsonString));
    } catch (e) {
      return fromJson({});
    }
  }

  List<T> _decodeJsonList<T>(String? jsonString, T Function(dynamic) fromItem) {
    if (jsonString == null || jsonString.isEmpty) {
      return [];
    }
    try {
      final List<dynamic> list = jsonDecode(jsonString);
      return list.map(fromItem).toList();
    } catch (e) {
      return [];
    }
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  // Clear all data (for testing or reset)
  Future<void> clearAllData() async {
    final db = await database;
    await db.transaction((txn) async {
      await txn.delete('email_tracking');
      await txn.delete('campaigns');
      await txn.delete('email_templates');
      await txn.delete('contact_groups');
      await txn.delete('contact_tags');
      await txn.delete('recipients');
      await txn.delete('email_service_configs');
      await txn.delete('email_service_usage');
      await txn.delete('auth_sessions');
      await txn.delete('user_passwords');
      await txn.delete('users');
    });
  }
}
