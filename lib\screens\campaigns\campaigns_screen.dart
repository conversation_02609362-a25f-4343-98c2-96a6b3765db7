// lib/screens/campaigns/campaigns_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/campaign_provider.dart';
import '../../models/email_campaign.dart' as email_campaign;

class CampaignsScreen extends StatefulWidget {
  const CampaignsScreen({super.key});

  @override
  State<CampaignsScreen> createState() => _CampaignsScreenState();
}

class _CampaignsScreenState extends State<CampaignsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CampaignProvider>().loadCampaigns();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Campaigns'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.all_inbox), text: 'All'),
            Tab(icon: Icon(Icons.schedule), text: 'Scheduled'),
            Tab(icon: Icon(Icons.send), text: 'Sent'),
            Tab(icon: Icon(Icons.drafts), text: 'Drafts'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _createNewCampaign(),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'analytics',
                    child: Text('Analytics'),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: Text('Settings'),
                  ),
                ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search campaigns...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            context.read<CampaignProvider>().searchCampaigns(
                              '',
                            );
                          },
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                context.read<CampaignProvider>().searchCampaigns(value);
              },
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCampaignsList(null), // All campaigns
                _buildCampaignsList(CampaignStatus.scheduled),
                _buildCampaignsList(CampaignStatus.sent),
                _buildCampaignsList(CampaignStatus.draft),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _createNewCampaign(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCampaignsList(email_campaign.CampaignStatus? statusFilter) {
    return Consumer<CampaignProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text('Error: ${provider.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => provider.loadCampaigns(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        var campaigns = provider.campaigns;
        if (statusFilter != null) {
          campaigns = campaigns.where((c) => c.status == statusFilter).toList();
        }

        if (campaigns.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.campaign_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  statusFilter == null
                      ? 'No campaigns found'
                      : 'No ${statusFilter.toString().split('.').last} campaigns',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Create your first email campaign to get started',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _createNewCampaign(),
                  icon: const Icon(Icons.add),
                  label: const Text('Create Campaign'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: campaigns.length,
          itemBuilder: (context, index) {
            final campaign = campaigns[index];
            return _buildCampaignCard(campaign);
          },
        );
      },
    );
  }

  Widget _buildCampaignCard(email_campaign.EmailCampaign campaign) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _openCampaign(campaign),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          campaign.name,
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          campaign.subject,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(campaign.status),
                  const SizedBox(width: 8),
                  PopupMenuButton<String>(
                    onSelected:
                        (value) => _handleCampaignAction(value, campaign),
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Text('Edit'),
                          ),
                          const PopupMenuItem(
                            value: 'duplicate',
                            child: Text('Duplicate'),
                          ),
                          const PopupMenuItem(
                            value: 'analytics',
                            child: Text('Analytics'),
                          ),
                          if (campaign.status == CampaignStatus.draft)
                            const PopupMenuItem(
                              value: 'send',
                              child: Text('Send Now'),
                            ),
                          if (campaign.status == CampaignStatus.scheduled)
                            const PopupMenuItem(
                              value: 'cancel',
                              child: Text('Cancel'),
                            ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Text('Delete'),
                          ),
                        ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Campaign Stats
              Row(
                children: [
                  _buildStatItem(
                    icon: Icons.people,
                    label: 'Recipients',
                    value: campaign.recipientCount.toString(),
                  ),
                  const SizedBox(width: 24),
                  if (campaign.status == CampaignStatus.sent) ...[
                    _buildStatItem(
                      icon: Icons.email,
                      label: 'Sent',
                      value: campaign.sentCount?.toString() ?? '0',
                    ),
                    const SizedBox(width: 24),
                    _buildStatItem(
                      icon: Icons.open_in_new,
                      label: 'Opens',
                      value: campaign.openCount?.toString() ?? '0',
                    ),
                    const SizedBox(width: 24),
                    _buildStatItem(
                      icon: Icons.mouse,
                      label: 'Clicks',
                      value: campaign.clickCount?.toString() ?? '0',
                    ),
                  ] else if (campaign.scheduledAt != null) ...[
                    _buildStatItem(
                      icon: Icons.schedule,
                      label: 'Scheduled',
                      value: _formatDateTime(campaign.scheduledAt!),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),

              // Progress Bar (for sending campaigns)
              if (campaign.status == CampaignStatus.sending) ...[
                LinearProgressIndicator(
                  value: campaign.progress,
                  backgroundColor: Colors.grey[300],
                ),
                const SizedBox(height: 8),
                Text(
                  'Sending... ${(campaign.progress * 100).toInt()}%',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],

              // Footer
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Created ${_formatDate(campaign.createdAt)}',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  ),
                  if (campaign.templateName?.isNotEmpty == true)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        campaign.templateName!,
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(email_campaign.CampaignStatus status) {
    Color color;
    String label;

    switch (status) {
      case CampaignStatus.draft:
        color = Colors.grey;
        label = 'Draft';
        break;
      case CampaignStatus.scheduled:
        color = Colors.orange;
        label = 'Scheduled';
        break;
      case CampaignStatus.sending:
        color = Colors.blue;
        label = 'Sending';
        break;
      case CampaignStatus.sent:
        color = Colors.green;
        label = 'Sent';
        break;
      case CampaignStatus.paused:
        color = Colors.amber;
        label = 'Paused';
        break;
      case CampaignStatus.cancelled:
        color = Colors.red;
        label = 'Cancelled';
        break;
      case CampaignStatus.failed:
        color = Colors.red;
        label = 'Failed';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _createNewCampaign() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create campaign - Coming soon')),
    );
  }

  void _openCampaign(email_campaign.EmailCampaign campaign) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Open campaign "${campaign.name}" - Coming soon')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'analytics':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Campaign analytics - Coming soon')),
        );
        break;
      case 'settings':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Campaign settings - Coming soon')),
        );
        break;
    }
  }

  void _handleCampaignAction(
    String action,
    email_campaign.EmailCampaign campaign,
  ) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Edit campaign "${campaign.name}" - Coming soon'),
          ),
        );
        break;
      case 'duplicate':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Duplicate campaign "${campaign.name}" - Coming soon',
            ),
          ),
        );
        break;
      case 'analytics':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Analytics for "${campaign.name}" - Coming soon'),
          ),
        );
        break;
      case 'send':
        _showSendConfirmationDialog(campaign);
        break;
      case 'cancel':
        _showCancelConfirmationDialog(campaign);
        break;
      case 'delete':
        _showDeleteConfirmationDialog(campaign);
        break;
    }
  }

  void _showSendConfirmationDialog(email_campaign.EmailCampaign campaign) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Send Campaign'),
            content: Text(
              'Are you sure you want to send "${campaign.name}" to ${campaign.recipientCount} recipients?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<CampaignProvider>().sendCampaign(campaign.id!);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Campaign "${campaign.name}" is being sent',
                      ),
                    ),
                  );
                },
                child: const Text('Send'),
              ),
            ],
          ),
    );
  }

  void _showCancelConfirmationDialog(email_campaign.EmailCampaign campaign) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Cancel Campaign'),
            content: Text(
              'Are you sure you want to cancel "${campaign.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('No'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<CampaignProvider>().cancelCampaign(campaign.id!);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Campaign "${campaign.name}" cancelled'),
                    ),
                  );
                },
                child: const Text('Cancel Campaign'),
              ),
            ],
          ),
    );
  }

  void _showDeleteConfirmationDialog(email_campaign.EmailCampaign campaign) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Campaign'),
            content: Text(
              'Are you sure you want to delete "${campaign.name}"? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<CampaignProvider>().deleteCampaign(campaign.id!);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Campaign "${campaign.name}" deleted'),
                    ),
                  );
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
