// lib/database/repositories/contact_groups_repository.dart
import 'package:sqflite/sqflite.dart';
import '../database_helper.dart';
import '../../models/contact_group_model.dart';

class ContactGroupsRepository {
  static final ContactGroupsRepository _instance =
      ContactGroupsRepository._internal();
  factory ContactGroupsRepository() => _instance;
  ContactGroupsRepository._internal();

  Future<Database> get _database async => await DatabaseHelper().database;

  // Create contact group
  Future<String> create(ContactGroup group) async {
    final db = await _database;
    final id = await db.insert('contact_groups', group.toJson());
    return id.toString();
  }

  // Get contact group by ID
  Future<ContactGroup?> getById(String id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      'contact_groups',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return ContactGroup.fromJson(maps.first);
    }
    return null;
  }

  // Get all contact groups
  Future<List<ContactGroup>> getAll({
    int? limit,
    int? offset,
    String? searchQuery,
    GroupType? type,
  }) async {
    final db = await _database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause = 'name LIKE ?';
      whereArgs.add('%$searchQuery%');
    }

    if (type != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'type = ?';
      whereArgs.add(type.toString());
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'contact_groups',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return ContactGroup.fromJson(maps[i]);
    });
  }

  // Update contact group
  Future<void> update(ContactGroup group) async {
    final db = await _database;
    await db.update(
      'contact_groups',
      group.toJson(),
      where: 'id = ?',
      whereArgs: [group.id],
    );
  }

  // Delete contact group
  Future<void> delete(String id) async {
    final db = await _database;
    await db.delete('contact_groups', where: 'id = ?', whereArgs: [id]);
  }

  // Get contact groups by recipient
  Future<List<ContactGroup>> getByRecipient(String recipientId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT cg.* FROM contact_groups cg
      INNER JOIN recipient_groups rg ON cg.id = rg.group_id
      WHERE rg.recipient_id = ?
    ''',
      [recipientId],
    );

    return List.generate(maps.length, (i) {
      return ContactGroup.fromJson(maps[i]);
    });
  }

  // Add recipient to group
  Future<void> addRecipientToGroup(String recipientId, String groupId) async {
    final db = await _database;
    await db.insert('recipient_groups', {
      'recipient_id': recipientId,
      'group_id': groupId,
      'added_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.ignore);
  }

  // Remove recipient from group
  Future<void> removeRecipientFromGroup(
    String recipientId,
    String groupId,
  ) async {
    final db = await _database;
    await db.delete(
      'recipient_groups',
      where: 'recipient_id = ? AND group_id = ?',
      whereArgs: [recipientId, groupId],
    );
  }

  // Get recipients in group
  Future<List<String>> getRecipientsInGroup(String groupId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipient_groups',
      columns: ['recipient_id'],
      where: 'group_id = ?',
      whereArgs: [groupId],
    );

    return maps.map((map) => map['recipient_id'] as String).toList();
  }

  // Get group statistics
  Future<Map<String, int>> getGroupStats() async {
    final db = await _database;

    final totalResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM contact_groups',
    );
    final staticResult = await db.rawQuery(
      "SELECT COUNT(*) as count FROM contact_groups WHERE type = 'static'",
    );
    final dynamicResult = await db.rawQuery(
      "SELECT COUNT(*) as count FROM contact_groups WHERE type = 'dynamic'",
    );

    return {
      'total': totalResult.first['count'] as int,
      'static': staticResult.first['count'] as int,
      'dynamic': dynamicResult.first['count'] as int,
    };
  }

  // Search groups
  Future<List<ContactGroup>> search(String query) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      'contact_groups',
      where: 'name LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return ContactGroup.fromJson(maps[i]);
    });
  }

  // Add multiple contacts to group
  Future<void> addContacts(String groupId, List<String> contactIds) async {
    final db = await _database;
    final batch = db.batch();

    for (final contactId in contactIds) {
      batch.insert('recipient_groups', {
        'recipient_id': contactId,
        'group_id': groupId,
        'added_at': DateTime.now().toIso8601String(),
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
    }

    await batch.commit();
  }

  // Remove multiple contacts from group
  Future<void> removeContacts(String groupId, List<String> contactIds) async {
    final db = await _database;
    final batch = db.batch();

    for (final contactId in contactIds) {
      batch.delete(
        'recipient_groups',
        where: 'recipient_id = ? AND group_id = ?',
        whereArgs: [contactId, groupId],
      );
    }

    await batch.commit();
  }
}
