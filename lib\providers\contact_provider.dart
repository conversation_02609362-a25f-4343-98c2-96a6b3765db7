// lib/providers/contact_provider.dart
import 'package:flutter/foundation.dart';
import '../models/recipient_model.dart';
import '../models/contact_group_model.dart';
import '../services/contact_service.dart';

class ContactProvider extends ChangeNotifier {
  final ContactService _contactService = ContactService();

  List<Recipient> _contacts = [];
  List<ContactGroup> _groups = [];
  List<String> _tags = [];
  Recipient? _selectedContact;
  ContactGroup? _selectedGroup;
  ContactStats? _stats;
  bool _isLoading = false;
  String? _error;

  // Pagination
  int _currentPage = 0;
  int _pageSize = 50;
  int _totalCount = 0;
  bool _hasMore = true;

  // Filters
  String? _searchQuery;
  List<String> _selectedTags = [];
  List<String> _selectedGroups = [];
  bool? _subscriptionFilter;

  // Selection
  Set<String> _selectedContactIds = {};

  // Getters
  List<Recipient> get contacts => _contacts;
  List<ContactGroup> get groups => _groups;
  List<String> get tags => _tags;
  List<String> get availableTags => _tags;
  Recipient? get selectedContact => _selectedContact;
  ContactGroup? get selectedGroup => _selectedGroup;
  ContactStats? get stats => _stats;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get totalCount => _totalCount;
  bool get hasMore => _hasMore;
  int get currentPage => _currentPage;
  String? get searchQuery => _searchQuery;
  List<String> get selectedTags => _selectedTags;
  List<String> get selectedGroups => _selectedGroups;
  bool? get subscriptionFilter => _subscriptionFilter;
  Set<String> get selectedContactIds => _selectedContactIds;
  bool get hasSelection => _selectedContactIds.isNotEmpty;
  int get selectionCount => _selectedContactIds.length;

  // Initialize contacts
  Future<void> initialize() async {
    await loadContacts(refresh: true);
    await loadGroups();
    await loadTags();
    await loadStats();
  }

  // Load contacts with pagination
  Future<void> loadContacts({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _contacts.clear();
      _hasMore = true;
      _selectedContactIds.clear();
    }

    if (!_hasMore || _isLoading) return;

    _setLoading(true);
    try {
      final offset = _currentPage * _pageSize;
      final newContacts = await _contactService.getContacts(
        limit: _pageSize,
        offset: offset,
        searchQuery: _searchQuery,
        tags: _selectedTags.isEmpty ? null : _selectedTags,
        groups: _selectedGroups.isEmpty ? null : _selectedGroups,
        isSubscribed: _subscriptionFilter,
      );

      final count = await _contactService.getContactCount(
        searchQuery: _searchQuery,
        tags: _selectedTags.isEmpty ? null : _selectedTags,
        groups: _selectedGroups.isEmpty ? null : _selectedGroups,
        isSubscribed: _subscriptionFilter,
      );

      if (refresh) {
        _contacts = newContacts;
      } else {
        _contacts.addAll(newContacts);
      }

      _totalCount = count;
      _hasMore = newContacts.length == _pageSize;
      _currentPage++;
      _clearError();
    } catch (e) {
      _setError('Failed to load contacts: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load more contacts (pagination)
  Future<void> loadMoreContacts() async {
    await loadContacts();
  }

  // Search contacts
  Future<void> searchContacts(String query) async {
    _searchQuery = query.isEmpty ? null : query;
    await loadContacts(refresh: true);
  }

  // Filter by tags
  Future<void> filterByTags(List<String> tags) async {
    _selectedTags = tags;
    await loadContacts(refresh: true);
  }

  // Filter by groups
  Future<void> filterByGroups(List<String> groups) async {
    _selectedGroups = groups;
    await loadContacts(refresh: true);
  }

  // Filter by subscription status
  Future<void> filterBySubscription(bool? isSubscribed) async {
    _subscriptionFilter = isSubscribed;
    await loadContacts(refresh: true);
  }

  // Clear all filters
  Future<void> clearFilters() async {
    _searchQuery = null;
    _selectedTags.clear();
    _selectedGroups.clear();
    _subscriptionFilter = null;
    await loadContacts(refresh: true);
  }

  // Contact CRUD operations
  Future<bool> createContact(Recipient contact) async {
    _setLoading(true);
    try {
      final id = await _contactService.createContact(contact);
      final createdContact = await _contactService.getContact(id);

      if (createdContact != null) {
        _contacts.insert(0, createdContact);
        _totalCount++;
        await loadStats(); // Refresh stats
        await loadTags(); // Refresh tags if new ones were added
        _clearError();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to create contact: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateContact(Recipient contact) async {
    _setLoading(true);
    try {
      await _contactService.updateContact(contact);

      // Update in local list
      final index = _contacts.indexWhere((c) => c.id == contact.id);
      if (index != -1) {
        _contacts[index] = contact;
      }

      // Update selected contact if it's the same
      if (_selectedContact?.id == contact.id) {
        _selectedContact = contact;
      }

      await loadStats(); // Refresh stats
      await loadTags(); // Refresh tags
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update contact: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteContact(String id) async {
    _setLoading(true);
    try {
      await _contactService.deleteContact(id);

      // Remove from local list
      _contacts.removeWhere((c) => c.id == id);
      _totalCount--;
      _selectedContactIds.remove(id);

      // Clear selected contact if it's the deleted one
      if (_selectedContact?.id == id) {
        _selectedContact = null;
      }

      await loadStats(); // Refresh stats
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete contact: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteSelectedContacts() async {
    if (_selectedContactIds.isEmpty) return false;

    _setLoading(true);
    try {
      await _contactService.deleteContacts(_selectedContactIds.toList());

      // Remove from local list
      _contacts.removeWhere((c) => _selectedContactIds.contains(c.id));
      _totalCount -= _selectedContactIds.length;

      // Clear selected contact if it was deleted
      if (_selectedContact != null &&
          _selectedContactIds.contains(_selectedContact!.id)) {
        _selectedContact = null;
      }

      _selectedContactIds.clear();
      await loadStats(); // Refresh stats
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete contacts: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Import/Export
  Future<ImportResult?> importFromCsv(
    String filePath, {
    bool hasHeaders = true,
    Map<String, String>? columnMapping,
  }) async {
    _setLoading(true);
    try {
      final result = await _contactService.importFromCsv(
        filePath,
        hasHeaders: hasHeaders,
        columnMapping: columnMapping,
      );

      await loadContacts(refresh: true);
      await loadStats();
      await loadTags();
      _clearError();
      return result;
    } catch (e) {
      _setError('Failed to import contacts: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> exportToCsv(
    String filePath, {
    List<Recipient>? contactsToExport,
  }) async {
    _setLoading(true);
    try {
      final contactsForExport =
          contactsToExport ??
          (hasSelection
              ? _contacts
                  .where((c) => _selectedContactIds.contains(c.id))
                  .toList()
              : _contacts);

      final exportPath = await _contactService.exportToCsv(
        contactsForExport,
        filePath,
      );
      _clearError();
      return exportPath;
    } catch (e) {
      _setError('Failed to export contacts: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Group management
  Future<void> loadGroups() async {
    try {
      _groups = await _contactService.getGroups();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load groups: ${e.toString()}');
    }
  }

  Future<bool> createGroup(ContactGroup group) async {
    _setLoading(true);
    try {
      final id = await _contactService.createGroup(group);
      final createdGroup = await _contactService.getGroup(id);

      if (createdGroup != null) {
        _groups.insert(0, createdGroup);
        _clearError();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to create group: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateGroup(ContactGroup group) async {
    _setLoading(true);
    try {
      await _contactService.updateGroup(group);

      // Update in local list
      final index = _groups.indexWhere((g) => g.id == group.id);
      if (index != -1) {
        _groups[index] = group;
      }

      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update group: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteGroup(String id) async {
    _setLoading(true);
    try {
      await _contactService.deleteGroup(id);

      // Remove from local list
      _groups.removeWhere((g) => g.id == id);

      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete group: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Tag management
  Future<void> loadTags() async {
    try {
      _tags = await _contactService.getAllTags();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load tags: ${e.toString()}');
    }
  }

  Future<bool> addTagsToSelectedContacts(List<String> tags) async {
    if (_selectedContactIds.isEmpty) return false;

    _setLoading(true);
    try {
      await _contactService.addTagsToContacts(
        _selectedContactIds.toList(),
        tags,
      );
      await loadContacts(refresh: true);
      await loadTags();
      _clearError();
      return true;
    } catch (e) {
      _setError('Failed to add tags: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> removeTagsFromSelectedContacts(List<String> tags) async {
    if (_selectedContactIds.isEmpty) return false;

    _setLoading(true);
    try {
      await _contactService.removeTagsFromContacts(
        _selectedContactIds.toList(),
        tags,
      );
      await loadContacts(refresh: true);
      await loadTags();
      _clearError();
      return true;
    } catch (e) {
      _setError('Failed to remove tags: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Selection management
  void selectContact(Recipient? contact) {
    _selectedContact = contact;
    notifyListeners();
  }

  void selectGroup(ContactGroup? group) {
    _selectedGroup = group;
    notifyListeners();
  }

  void toggleContactSelection(String contactId) {
    if (_selectedContactIds.contains(contactId)) {
      _selectedContactIds.remove(contactId);
    } else {
      _selectedContactIds.add(contactId);
    }
    notifyListeners();
  }

  void selectAllContacts() {
    _selectedContactIds = _contacts.map((c) => c.id!).toSet();
    notifyListeners();
  }

  void clearSelection() {
    _selectedContactIds.clear();
    notifyListeners();
  }

  // Statistics
  Future<void> loadStats() async {
    try {
      _stats = await _contactService.getContactStats();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load contact statistics: ${e.toString()}');
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Export contacts
  Future<String?> exportContacts() async {
    _setLoading(true);
    _clearError();

    try {
      return await _contactService.exportContacts(_contacts);
    } catch (e) {
      _setError('Failed to export contacts: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Import contacts
  Future<bool> importContacts(List<Map<String, dynamic>> contactsData) async {
    _setLoading(true);
    _clearError();

    try {
      await _contactService.importContacts(contactsData);
      await loadContacts(); // Reload contacts after import
      return true;
    } catch (e) {
      _setError('Failed to import contacts: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Add contact
  Future<bool> addContact(Recipient contact) async {
    _setLoading(true);
    _clearError();

    try {
      await _contactService.addContact(contact);
      await loadContacts(); // Reload contacts after adding
      return true;
    } catch (e) {
      _setError('Failed to add contact: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Clear all data
  void clear() {
    _contacts.clear();
    _groups.clear();
    _tags.clear();
    _selectedContact = null;
    _selectedGroup = null;
    _stats = null;
    _selectedContactIds.clear();
    _currentPage = 0;
    _totalCount = 0;
    _hasMore = true;
    _searchQuery = null;
    _selectedTags.clear();
    _selectedGroups.clear();
    _subscriptionFilter = null;
    _clearError();
    notifyListeners();
  }
}
