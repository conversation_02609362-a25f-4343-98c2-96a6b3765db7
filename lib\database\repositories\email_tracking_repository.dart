// lib/database/repositories/email_tracking_repository.dart
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../database_helper.dart';
import '../../models/email_tracking_model.dart';

class EmailTrackingRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  Future<String> create(EmailTracking tracking) async {
    final db = await _dbHelper.database;
    final id = tracking.id ?? _uuid.v4();
    
    final trackingWithId = tracking.copyWith(id: id);

    await db.insert(
      'email_tracking',
      {
        'id': trackingWithId.id,
        'campaign_id': trackingWithId.campaignId,
        'recipient_id': trackingWithId.recipientId,
        'recipient_email': trackingWithId.recipientEmail,
        'status': trackingWithId.status.toString().split('.').last,
        'created_at': trackingWithId.createdAt.toIso8601String(),
        'sent_at': trackingWithId.sentAt?.toIso8601String(),
        'delivered_at': trackingWithId.deliveredAt?.toIso8601String(),
        'opened_at': trackingWithId.openedAt?.toIso8601String(),
        'clicked_at': trackingWithId.clickedAt?.toIso8601String(),
        'bounced_at': trackingWithId.bouncedAt?.toIso8601String(),
        'failed_at': trackingWithId.failedAt?.toIso8601String(),
        'error_message': trackingWithId.errorMessage,
        'bounce_type': trackingWithId.bounceType?.toString().split('.').last,
        'bounce_reason': trackingWithId.bounceReason,
        'metadata': jsonEncode(trackingWithId.metadata),
        'events': jsonEncode(trackingWithId.events.map((e) => e.toJson()).toList()),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    return id;
  }

  Future<List<String>> createBatch(List<EmailTracking> trackingList) async {
    final db = await _dbHelper.database;
    final ids = <String>[];

    await db.transaction((txn) async {
      for (final tracking in trackingList) {
        final id = tracking.id ?? _uuid.v4();
        ids.add(id);
        
        final trackingWithId = tracking.copyWith(id: id);

        await txn.insert(
          'email_tracking',
          {
            'id': trackingWithId.id,
            'campaign_id': trackingWithId.campaignId,
            'recipient_id': trackingWithId.recipientId,
            'recipient_email': trackingWithId.recipientEmail,
            'status': trackingWithId.status.toString().split('.').last,
            'created_at': trackingWithId.createdAt.toIso8601String(),
            'sent_at': trackingWithId.sentAt?.toIso8601String(),
            'delivered_at': trackingWithId.deliveredAt?.toIso8601String(),
            'opened_at': trackingWithId.openedAt?.toIso8601String(),
            'clicked_at': trackingWithId.clickedAt?.toIso8601String(),
            'bounced_at': trackingWithId.bouncedAt?.toIso8601String(),
            'failed_at': trackingWithId.failedAt?.toIso8601String(),
            'error_message': trackingWithId.errorMessage,
            'bounce_type': trackingWithId.bounceType?.toString().split('.').last,
            'bounce_reason': trackingWithId.bounceReason,
            'metadata': jsonEncode(trackingWithId.metadata),
            'events': jsonEncode(trackingWithId.events.map((e) => e.toJson()).toList()),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });

    return ids;
  }

  Future<EmailTracking?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'email_tracking',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToEmailTracking(maps.first);
  }

  Future<EmailTracking?> getByCampaignAndRecipient(String campaignId, String recipientId) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'email_tracking',
      where: 'campaign_id = ? AND recipient_id = ?',
      whereArgs: [campaignId, recipientId],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToEmailTracking(maps.first);
  }

  Future<List<EmailTracking>> getByCampaign(String campaignId, {
    int? limit,
    int? offset,
    EmailStatus? status,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = 'campaign_id = ?';
    List<dynamic> whereArgs = [campaignId];

    if (status != null) {
      whereClause += ' AND status = ?';
      whereArgs.add(status.toString().split('.').last);
    }

    final maps = await db.query(
      'email_tracking',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map(_mapToEmailTracking).toList();
  }

  Future<List<EmailTracking>> getByStatus(EmailStatus status, {
    int? limit,
    int? offset,
  }) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'email_tracking',
      where: 'status = ?',
      whereArgs: [status.toString().split('.').last],
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map(_mapToEmailTracking).toList();
  }

  Future<void> updateStatus(String id, EmailStatus status, {
    DateTime? timestamp,
    String? errorMessage,
    BounceType? bounceType,
    String? bounceReason,
  }) async {
    final db = await _dbHelper.database;
    
    final updateData = <String, dynamic>{
      'status': status.toString().split('.').last,
    };

    final now = timestamp ?? DateTime.now();
    final nowString = now.toIso8601String();

    switch (status) {
      case EmailStatus.sent:
        updateData['sent_at'] = nowString;
        break;
      case EmailStatus.delivered:
        updateData['delivered_at'] = nowString;
        break;
      case EmailStatus.opened:
        updateData['opened_at'] = nowString;
        break;
      case EmailStatus.clicked:
        updateData['clicked_at'] = nowString;
        break;
      case EmailStatus.bounced:
        updateData['bounced_at'] = nowString;
        if (bounceType != null) {
          updateData['bounce_type'] = bounceType.toString().split('.').last;
        }
        if (bounceReason != null) {
          updateData['bounce_reason'] = bounceReason;
        }
        break;
      case EmailStatus.failed:
        updateData['failed_at'] = nowString;
        if (errorMessage != null) {
          updateData['error_message'] = errorMessage;
        }
        break;
      default:
        break;
    }

    await db.update(
      'email_tracking',
      updateData,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> addEvent(String trackingId, EmailEvent event) async {
    final tracking = await getById(trackingId);
    if (tracking == null) return;

    final updatedEvents = [...tracking.events, event];
    
    await update(tracking.copyWith(events: updatedEvents));
  }

  Future<void> update(EmailTracking tracking) async {
    final db = await _dbHelper.database;

    await db.update(
      'email_tracking',
      {
        'campaign_id': tracking.campaignId,
        'recipient_id': tracking.recipientId,
        'recipient_email': tracking.recipientEmail,
        'status': tracking.status.toString().split('.').last,
        'sent_at': tracking.sentAt?.toIso8601String(),
        'delivered_at': tracking.deliveredAt?.toIso8601String(),
        'opened_at': tracking.openedAt?.toIso8601String(),
        'clicked_at': tracking.clickedAt?.toIso8601String(),
        'bounced_at': tracking.bouncedAt?.toIso8601String(),
        'failed_at': tracking.failedAt?.toIso8601String(),
        'error_message': tracking.errorMessage,
        'bounce_type': tracking.bounceType?.toString().split('.').last,
        'bounce_reason': tracking.bounceReason,
        'metadata': jsonEncode(tracking.metadata),
        'events': jsonEncode(tracking.events.map((e) => e.toJson()).toList()),
      },
      where: 'id = ?',
      whereArgs: [tracking.id],
    );
  }

  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'email_tracking',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteByCampaign(String campaignId) async {
    final db = await _dbHelper.database;
    await db.delete(
      'email_tracking',
      where: 'campaign_id = ?',
      whereArgs: [campaignId],
    );
  }

  Future<Map<EmailStatus, int>> getStatusCounts(String campaignId) async {
    final db = await _dbHelper.database;
    final maps = await db.rawQuery('''
      SELECT status, COUNT(*) as count 
      FROM email_tracking 
      WHERE campaign_id = ? 
      GROUP BY status
    ''', [campaignId]);

    final counts = <EmailStatus, int>{};
    for (final map in maps) {
      final status = EmailStatus.values.firstWhere(
        (e) => e.toString() == 'EmailStatus.${map['status']}',
        orElse: () => EmailStatus.queued,
      );
      counts[status] = map['count'] as int;
    }

    return counts;
  }

  EmailTracking _mapToEmailTracking(Map<String, dynamic> map) {
    return EmailTracking(
      id: map['id'],
      campaignId: map['campaign_id'],
      recipientId: map['recipient_id'],
      recipientEmail: map['recipient_email'],
      status: EmailStatus.values.firstWhere(
        (e) => e.toString() == 'EmailStatus.${map['status']}',
        orElse: () => EmailStatus.queued,
      ),
      createdAt: DateTime.parse(map['created_at']),
      sentAt: map['sent_at'] != null ? DateTime.parse(map['sent_at']) : null,
      deliveredAt: map['delivered_at'] != null ? DateTime.parse(map['delivered_at']) : null,
      openedAt: map['opened_at'] != null ? DateTime.parse(map['opened_at']) : null,
      clickedAt: map['clicked_at'] != null ? DateTime.parse(map['clicked_at']) : null,
      bouncedAt: map['bounced_at'] != null ? DateTime.parse(map['bounced_at']) : null,
      failedAt: map['failed_at'] != null ? DateTime.parse(map['failed_at']) : null,
      errorMessage: map['error_message'],
      bounceType: map['bounce_type'] != null 
        ? BounceType.values.firstWhere(
            (e) => e.toString() == 'BounceType.${map['bounce_type']}',
            orElse: () => BounceType.unknown,
          )
        : null,
      bounceReason: map['bounce_reason'],
      metadata: _parseJson(map['metadata']),
      events: _parseEventsList(map['events']),
    );
  }

  Map<String, dynamic> _parseJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return {};
    try {
      return Map<String, dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      return {};
    }
  }

  List<EmailEvent> _parseEventsList(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return [];
    try {
      final List<dynamic> list = jsonDecode(jsonString);
      return list.map((e) => EmailEvent.fromJson(e)).toList();
    } catch (e) {
      return [];
    }
  }
}
