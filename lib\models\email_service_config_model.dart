// lib/models/email_service_config_model.dart

enum EmailServiceProvider {
  sendgrid,
  mailgun,
  awsSes,
  smtp,
  postmark,
  mailchimp,
}

class EmailServiceConfig {
  final String? id;
  final String name;
  final EmailServiceProvider provider;
  final Map<String, dynamic> credentials;
  final Map<String, dynamic> settings;
  final bool isActive;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int? dailyLimit;
  final int? monthlyLimit;
  final int? rateLimit; // emails per minute

  EmailServiceConfig({
    this.id,
    required this.name,
    required this.provider,
    this.credentials = const {},
    this.settings = const {},
    this.isActive = true,
    this.isDefault = false,
    DateTime? createdAt,
    this.updatedAt,
    this.dailyLimit,
    this.monthlyLimit,
    this.rateLimit,
  }) : createdAt = createdAt ?? DateTime.now();

  factory EmailServiceConfig.fromJson(Map<String, dynamic> json) {
    return EmailServiceConfig(
      id: json['id'],
      name: json['name'],
      provider: EmailServiceProvider.values.firstWhere(
        (e) => e.toString() == 'EmailServiceProvider.${json['provider']}',
        orElse: () => EmailServiceProvider.smtp,
      ),
      credentials: Map<String, dynamic>.from(json['credentials'] ?? {}),
      settings: Map<String, dynamic>.from(json['settings'] ?? {}),
      isActive: json['isActive'] ?? true,
      isDefault: json['isDefault'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      dailyLimit: json['dailyLimit'],
      monthlyLimit: json['monthlyLimit'],
      rateLimit: json['rateLimit'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'provider': provider.toString().split('.').last,
      'credentials': credentials,
      'settings': settings,
      'isActive': isActive,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'dailyLimit': dailyLimit,
      'monthlyLimit': monthlyLimit,
      'rateLimit': rateLimit,
    };
  }

  EmailServiceConfig copyWith({
    String? id,
    String? name,
    EmailServiceProvider? provider,
    Map<String, dynamic>? credentials,
    Map<String, dynamic>? settings,
    bool? isActive,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? dailyLimit,
    int? monthlyLimit,
    int? rateLimit,
  }) {
    return EmailServiceConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      provider: provider ?? this.provider,
      credentials: credentials ?? this.credentials,
      settings: settings ?? this.settings,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      dailyLimit: dailyLimit ?? this.dailyLimit,
      monthlyLimit: monthlyLimit ?? this.monthlyLimit,
      rateLimit: rateLimit ?? this.rateLimit,
    );
  }

  // Helper methods for different providers
  String? get apiKey {
    switch (provider) {
      case EmailServiceProvider.sendgrid:
        return credentials['apiKey'];
      case EmailServiceProvider.mailgun:
        return credentials['apiKey'];
      case EmailServiceProvider.awsSes:
        return credentials['accessKey'];
      case EmailServiceProvider.postmark:
        return credentials['serverToken'];
      case EmailServiceProvider.mailchimp:
        return credentials['apiKey'];
      case EmailServiceProvider.smtp:
        return null;
    }
  }

  String? get smtpHost => credentials['host'];
  int? get smtpPort => credentials['port'];
  String? get smtpUsername => credentials['username'];
  String? get smtpPassword => credentials['password'];
  bool get smtpUseSsl => credentials['useSsl'] ?? true;

  String? get mailgunDomain => credentials['domain'];
  String? get awsRegion => credentials['region'];
  String? get awsSecretKey => credentials['secretKey'];

  bool get hasValidCredentials {
    switch (provider) {
      case EmailServiceProvider.sendgrid:
        return apiKey != null && apiKey!.isNotEmpty;
      case EmailServiceProvider.mailgun:
        return apiKey != null && apiKey!.isNotEmpty && mailgunDomain != null;
      case EmailServiceProvider.awsSes:
        return apiKey != null && awsSecretKey != null && awsRegion != null;
      case EmailServiceProvider.postmark:
        return apiKey != null && apiKey!.isNotEmpty;
      case EmailServiceProvider.mailchimp:
        return apiKey != null && apiKey!.isNotEmpty;
      case EmailServiceProvider.smtp:
        return smtpHost != null && 
               smtpPort != null && 
               smtpUsername != null && 
               smtpPassword != null;
    }
  }
}

class EmailServiceUsage {
  final String serviceConfigId;
  final DateTime date;
  final int emailsSent;
  final int emailsDelivered;
  final int emailsBounced;
  final int emailsFailed;
  final double cost;

  EmailServiceUsage({
    required this.serviceConfigId,
    required this.date,
    this.emailsSent = 0,
    this.emailsDelivered = 0,
    this.emailsBounced = 0,
    this.emailsFailed = 0,
    this.cost = 0.0,
  });

  factory EmailServiceUsage.fromJson(Map<String, dynamic> json) {
    return EmailServiceUsage(
      serviceConfigId: json['serviceConfigId'],
      date: DateTime.parse(json['date']),
      emailsSent: json['emailsSent'] ?? 0,
      emailsDelivered: json['emailsDelivered'] ?? 0,
      emailsBounced: json['emailsBounced'] ?? 0,
      emailsFailed: json['emailsFailed'] ?? 0,
      cost: (json['cost'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceConfigId': serviceConfigId,
      'date': date.toIso8601String(),
      'emailsSent': emailsSent,
      'emailsDelivered': emailsDelivered,
      'emailsBounced': emailsBounced,
      'emailsFailed': emailsFailed,
      'cost': cost,
    };
  }

  double get deliveryRate => emailsSent > 0 ? (emailsDelivered / emailsSent) * 100 : 0;
  double get bounceRate => emailsSent > 0 ? (emailsBounced / emailsSent) * 100 : 0;
  double get failureRate => emailsSent > 0 ? (emailsFailed / emailsSent) * 100 : 0;
}
