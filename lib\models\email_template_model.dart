// lib/models/email_template_model.dart

enum TemplateType {
  html,
  plainText,
  mixed,
}

class EmailTemplate {
  final String? id;
  final String name;
  final String subject;
  final String htmlContent;
  final String? plainTextContent;
  final TemplateType type;
  final List<String> variables; // Available variables like {{name}}, {{company}}
  final Map<String, dynamic> metadata;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? previewImageUrl;
  final String? category;

  EmailTemplate({
    this.id,
    required this.name,
    required this.subject,
    required this.htmlContent,
    this.plainTextContent,
    this.type = TemplateType.html,
    this.variables = const [],
    this.metadata = const {},
    this.isActive = true,
    DateTime? createdAt,
    this.updatedAt,
    this.previewImageUrl,
    this.category,
  }) : createdAt = createdAt ?? DateTime.now();

  factory EmailTemplate.fromJson(Map<String, dynamic> json) {
    return EmailTemplate(
      id: json['id'],
      name: json['name'],
      subject: json['subject'],
      htmlContent: json['htmlContent'],
      plainTextContent: json['plainTextContent'],
      type: TemplateType.values.firstWhere(
        (e) => e.toString() == 'TemplateType.${json['type']}',
        orElse: () => TemplateType.html,
      ),
      variables: List<String>.from(json['variables'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      previewImageUrl: json['previewImageUrl'],
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'htmlContent': htmlContent,
      'plainTextContent': plainTextContent,
      'type': type.toString().split('.').last,
      'variables': variables,
      'metadata': metadata,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'previewImageUrl': previewImageUrl,
      'category': category,
    };
  }

  EmailTemplate copyWith({
    String? id,
    String? name,
    String? subject,
    String? htmlContent,
    String? plainTextContent,
    TemplateType? type,
    List<String>? variables,
    Map<String, dynamic>? metadata,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? previewImageUrl,
    String? category,
  }) {
    return EmailTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      htmlContent: htmlContent ?? this.htmlContent,
      plainTextContent: plainTextContent ?? this.plainTextContent,
      type: type ?? this.type,
      variables: variables ?? this.variables,
      metadata: metadata ?? this.metadata,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      previewImageUrl: previewImageUrl ?? this.previewImageUrl,
      category: category ?? this.category,
    );
  }

  // Extract variables from template content
  List<String> extractVariables() {
    final regex = RegExp(r'\{\{(\w+)\}\}');
    final matches = regex.allMatches('$subject $htmlContent ${plainTextContent ?? ''}');
    return matches.map((match) => match.group(1)!).toSet().toList();
  }

  // Replace variables in template with actual values
  String renderSubject(Map<String, String> values) {
    return _replaceVariables(subject, values);
  }

  String renderHtmlContent(Map<String, String> values) {
    return _replaceVariables(htmlContent, values);
  }

  String? renderPlainTextContent(Map<String, String> values) {
    return plainTextContent != null ? _replaceVariables(plainTextContent!, values) : null;
  }

  String _replaceVariables(String content, Map<String, String> values) {
    String result = content;
    for (final entry in values.entries) {
      result = result.replaceAll('{{${entry.key}}}', entry.value);
    }
    return result;
  }
}
