// lib/models/contact.dart
import 'package:uuid/uuid.dart';

class Contact {
  final String id;
  final String name;
  final String email;
  final String? company;
  final String? position;
  final String? phone;
  final String? website;
  final String? address;
  final String? notes;
  final List<String> tags;
  final List<String> groups;
  final Map<String, dynamic> customFields;
  final bool isSubscribed;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? unsubscribedAt;
  final String? unsubscribeToken;

  Contact({
    String? id,
    required this.name,
    required this.email,
    this.company,
    this.position,
    this.phone,
    this.website,
    this.address,
    this.notes,
    List<String>? tags,
    List<String>? groups,
    Map<String, dynamic>? customFields,
    this.isSubscribed = true,
    DateTime? createdAt,
    this.updatedAt,
    this.unsubscribedAt,
    this.unsubscribeToken,
  }) : id = id ?? const Uuid().v4(),
       tags = tags ?? [],
       groups = groups ?? [],
       customFields = customFields ?? {},
       createdAt = createdAt ?? DateTime.now();

  Contact copyWith({
    String? id,
    String? name,
    String? email,
    String? company,
    String? position,
    String? phone,
    String? website,
    String? address,
    String? notes,
    List<String>? tags,
    List<String>? groups,
    Map<String, dynamic>? customFields,
    bool? isSubscribed,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? unsubscribedAt,
    String? unsubscribeToken,
  }) {
    return Contact(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      company: company ?? this.company,
      position: position ?? this.position,
      phone: phone ?? this.phone,
      website: website ?? this.website,
      address: address ?? this.address,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
      groups: groups ?? this.groups,
      customFields: customFields ?? this.customFields,
      isSubscribed: isSubscribed ?? this.isSubscribed,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      unsubscribedAt: unsubscribedAt ?? this.unsubscribedAt,
      unsubscribeToken: unsubscribeToken ?? this.unsubscribeToken,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'company': company,
      'position': position,
      'phone': phone,
      'website': website,
      'address': address,
      'notes': notes,
      'tags': tags.join(','),
      'groups': groups.join(','),
      'custom_fields': customFields.isNotEmpty ? 
          customFields.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
      'is_subscribed': isSubscribed ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'unsubscribed_at': unsubscribedAt?.toIso8601String(),
      'unsubscribe_token': unsubscribeToken,
    };
  }

  factory Contact.fromMap(Map<String, dynamic> map) {
    final customFieldsStr = map['custom_fields'] as String?;
    final customFields = <String, dynamic>{};
    
    if (customFieldsStr != null && customFieldsStr.isNotEmpty) {
      for (final pair in customFieldsStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          customFields[parts[0]] = parts[1];
        }
      }
    }

    return Contact(
      id: map['id'] as String,
      name: map['name'] as String,
      email: map['email'] as String,
      company: map['company'] as String?,
      position: map['position'] as String?,
      phone: map['phone'] as String?,
      website: map['website'] as String?,
      address: map['address'] as String?,
      notes: map['notes'] as String?,
      tags: (map['tags'] as String?)?.split(',').where((t) => t.isNotEmpty).toList() ?? [],
      groups: (map['groups'] as String?)?.split(',').where((g) => g.isNotEmpty).toList() ?? [],
      customFields: customFields,
      isSubscribed: (map['is_subscribed'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at'] as String) : null,
      unsubscribedAt: map['unsubscribed_at'] != null ? DateTime.parse(map['unsubscribed_at'] as String) : null,
      unsubscribeToken: map['unsubscribe_token'] as String?,
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory Contact.fromJson(Map<String, dynamic> json) => Contact.fromMap(json);

  @override
  String toString() {
    return 'Contact(id: $id, name: $name, email: $email, company: $company)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Contact && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  String get displayName => name.isNotEmpty ? name : email;
  
  String get fullContactInfo {
    final parts = <String>[];
    parts.add(displayName);
    if (company != null && company!.isNotEmpty) {
      parts.add(company!);
    }
    if (position != null && position!.isNotEmpty) {
      parts.add(position!);
    }
    return parts.join(' - ');
  }

  bool hasTag(String tag) => tags.contains(tag);
  
  bool hasGroup(String group) => groups.contains(group);
  
  bool matchesSearch(String query) {
    final lowerQuery = query.toLowerCase();
    return name.toLowerCase().contains(lowerQuery) ||
           email.toLowerCase().contains(lowerQuery) ||
           (company?.toLowerCase().contains(lowerQuery) ?? false) ||
           (position?.toLowerCase().contains(lowerQuery) ?? false) ||
           tags.any((tag) => tag.toLowerCase().contains(lowerQuery)) ||
           groups.any((group) => group.toLowerCase().contains(lowerQuery));
  }

  Contact addTag(String tag) {
    if (!tags.contains(tag)) {
      return copyWith(tags: [...tags, tag], updatedAt: DateTime.now());
    }
    return this;
  }

  Contact removeTag(String tag) {
    if (tags.contains(tag)) {
      return copyWith(
        tags: tags.where((t) => t != tag).toList(),
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  Contact addGroup(String group) {
    if (!groups.contains(group)) {
      return copyWith(groups: [...groups, group], updatedAt: DateTime.now());
    }
    return this;
  }

  Contact removeGroup(String group) {
    if (groups.contains(group)) {
      return copyWith(
        groups: groups.where((g) => g != group).toList(),
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  Contact updateCustomField(String key, dynamic value) {
    final newCustomFields = Map<String, dynamic>.from(customFields);
    newCustomFields[key] = value;
    return copyWith(customFields: newCustomFields, updatedAt: DateTime.now());
  }

  Contact removeCustomField(String key) {
    final newCustomFields = Map<String, dynamic>.from(customFields);
    newCustomFields.remove(key);
    return copyWith(customFields: newCustomFields, updatedAt: DateTime.now());
  }

  Contact unsubscribe() {
    return copyWith(
      isSubscribed: false,
      unsubscribedAt: DateTime.now(),
      unsubscribeToken: const Uuid().v4(),
      updatedAt: DateTime.now(),
    );
  }

  Contact resubscribe() {
    return copyWith(
      isSubscribed: true,
      unsubscribedAt: null,
      unsubscribeToken: null,
      updatedAt: DateTime.now(),
    );
  }
}
