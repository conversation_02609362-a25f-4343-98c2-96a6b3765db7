// lib/services/analytics_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../repositories/campaign_repository.dart';
import '../repositories/contact_repository.dart';
import '../repositories/email_tracking_repository.dart';
import '../providers/analytics_provider.dart';

class AnalyticsService {
  final CampaignRepository _campaignRepository = CampaignRepository();
  final ContactRepository _contactRepository = ContactRepository();
  final EmailTrackingRepository _trackingRepository = EmailTrackingRepository();

  Future<Map<String, dynamic>> getOverviewMetrics(String period) async {
    try {
      final campaigns = await _campaignRepository.getAllCampaigns();
      final contacts = await _contactRepository.getAllContacts();
      
      // Filter campaigns by period
      final filteredCampaigns = _filterByPeriod(campaigns, period);
      
      int totalEmailsSent = 0;
      double totalOpenRate = 0.0;
      double totalClickRate = 0.0;
      int campaignsWithStats = 0;
      
      for (final campaign in filteredCampaigns) {
        final stats = await _trackingRepository.getCampaignStats(campaign.id);
        if (stats != null) {
          totalEmailsSent += stats['sent'] ?? 0;
          if (stats['sent'] > 0) {
            totalOpenRate += (stats['opened'] ?? 0) / stats['sent'] * 100;
            totalClickRate += (stats['clicked'] ?? 0) / stats['sent'] * 100;
            campaignsWithStats++;
          }
        }
      }
      
      return {
        'totalCampaigns': filteredCampaigns.length,
        'totalEmailsSent': totalEmailsSent,
        'averageOpenRate': campaignsWithStats > 0 ? totalOpenRate / campaignsWithStats : 0.0,
        'averageClickRate': campaignsWithStats > 0 ? totalClickRate / campaignsWithStats : 0.0,
        'totalContacts': contacts.length,
      };
    } catch (e) {
      return {
        'totalCampaigns': 0,
        'totalEmailsSent': 0,
        'averageOpenRate': 0.0,
        'averageClickRate': 0.0,
        'totalContacts': 0,
      };
    }
  }

  Future<Map<String, dynamic>> getTrends(String period) async {
    try {
      // Get current period metrics
      final currentMetrics = await getOverviewMetrics(period);
      
      // Get previous period metrics for comparison
      final previousPeriod = _getPreviousPeriod(period);
      final previousMetrics = await getOverviewMetrics(previousPeriod);
      
      return {
        'campaignsTrend': _calculateTrend(
          currentMetrics['totalCampaigns'], 
          previousMetrics['totalCampaigns']
        ),
        'emailsTrend': _calculateTrend(
          currentMetrics['totalEmailsSent'], 
          previousMetrics['totalEmailsSent']
        ),
        'openRateTrend': _calculateTrend(
          currentMetrics['averageOpenRate'], 
          previousMetrics['averageOpenRate']
        ),
        'clickRateTrend': _calculateTrend(
          currentMetrics['averageClickRate'], 
          previousMetrics['averageClickRate']
        ),
      };
    } catch (e) {
      return {
        'campaignsTrend': 0.0,
        'emailsTrend': 0.0,
        'openRateTrend': 0.0,
        'clickRateTrend': 0.0,
      };
    }
  }

  Future<List<CampaignAnalytics>> getCampaignAnalytics(String period) async {
    try {
      final campaigns = await _campaignRepository.getAllCampaigns();
      final filteredCampaigns = _filterByPeriod(campaigns, period);
      
      List<CampaignAnalytics> analytics = [];
      
      for (final campaign in filteredCampaigns) {
        final stats = await _trackingRepository.getCampaignStats(campaign.id);
        if (stats != null) {
          final totalRecipients = stats['sent'] ?? 0;
          final opened = stats['opened'] ?? 0;
          final clicked = stats['clicked'] ?? 0;
          final bounced = stats['bounced'] ?? 0;
          
          analytics.add(CampaignAnalytics(
            id: campaign.id,
            name: campaign.name,
            sentDate: campaign.scheduledAt ?? campaign.createdAt,
            totalRecipients: totalRecipients,
            delivered: totalRecipients - bounced,
            opened: opened,
            clicked: clicked,
            bounced: bounced,
            unsubscribed: stats['unsubscribed'] ?? 0,
            openRate: totalRecipients > 0 ? (opened / totalRecipients) * 100 : 0.0,
            clickRate: totalRecipients > 0 ? (clicked / totalRecipients) * 100 : 0.0,
            bounceRate: totalRecipients > 0 ? (bounced / totalRecipients) * 100 : 0.0,
          ));
        }
      }
      
      return analytics;
    } catch (e) {
      return [];
    }
  }

  Future<List<ContactAnalytics>> getContactAnalytics(String period) async {
    try {
      final contacts = await _contactRepository.getAllContacts();
      List<ContactAnalytics> analytics = [];
      
      for (final contact in contacts) {
        final engagement = await _trackingRepository.getContactEngagement(contact.id);
        
        analytics.add(ContactAnalytics(
          id: contact.id,
          email: contact.email,
          name: contact.name,
          emailsReceived: engagement['received'] ?? 0,
          emailsOpened: engagement['opened'] ?? 0,
          emailsClicked: engagement['clicked'] ?? 0,
          lastActivity: engagement['lastActivity'] != null 
            ? DateTime.parse(engagement['lastActivity'])
            : contact.createdAt,
          engagementScore: _calculateEngagementScore(engagement),
        ));
      }
      
      return analytics;
    } catch (e) {
      return [];
    }
  }

  Future<Map<String, dynamic>> getPerformanceData(String period) async {
    try {
      final campaigns = await _campaignRepository.getAllCampaigns();
      final filteredCampaigns = _filterByPeriod(campaigns, period);
      
      Map<String, List<double>> dailyStats = {};
      
      for (final campaign in filteredCampaigns) {
        final stats = await _trackingRepository.getCampaignStats(campaign.id);
        if (stats != null) {
          final date = (campaign.scheduledAt ?? campaign.createdAt).toIso8601String().split('T')[0];
          
          if (!dailyStats.containsKey(date)) {
            dailyStats[date] = [0, 0, 0]; // [sent, opened, clicked]
          }
          
          dailyStats[date]![0] += stats['sent'] ?? 0;
          dailyStats[date]![1] += stats['opened'] ?? 0;
          dailyStats[date]![2] += stats['clicked'] ?? 0;
        }
      }
      
      return {
        'dailyStats': dailyStats,
        'totalCampaigns': filteredCampaigns.length,
      };
    } catch (e) {
      return {};
    }
  }

  Future<void> exportReport(String format, String period) async {
    try {
      final overview = await getOverviewMetrics(period);
      final campaigns = await getCampaignAnalytics(period);
      final contacts = await getContactAnalytics(period);
      
      final reportData = {
        'period': period,
        'generatedAt': DateTime.now().toIso8601String(),
        'overview': overview,
        'campaigns': campaigns.map((c) => {
          'id': c.id,
          'name': c.name,
          'sentDate': c.sentDate.toIso8601String(),
          'totalRecipients': c.totalRecipients,
          'delivered': c.delivered,
          'opened': c.opened,
          'clicked': c.clicked,
          'bounced': c.bounced,
          'openRate': c.openRate,
          'clickRate': c.clickRate,
          'bounceRate': c.bounceRate,
        }).toList(),
        'topContacts': contacts.take(50).map((c) => {
          'email': c.email,
          'name': c.name,
          'emailsReceived': c.emailsReceived,
          'emailsOpened': c.emailsOpened,
          'emailsClicked': c.emailsClicked,
          'engagementScore': c.engagementScore,
        }).toList(),
      };
      
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'email_marketing_report_${period}_${DateTime.now().millisecondsSinceEpoch}.$format';
      final file = File('${directory.path}/$fileName');
      
      if (format.toLowerCase() == 'json') {
        await file.writeAsString(jsonEncode(reportData));
      } else if (format.toLowerCase() == 'csv') {
        await file.writeAsString(_generateCSVReport(reportData));
      }
      
    } catch (e) {
      throw Exception('Failed to export report: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getCampaignPerformanceData(String period) async {
    final analytics = await getCampaignAnalytics(period);
    return analytics.map((a) => {
      'name': a.name,
      'openRate': a.openRate,
      'clickRate': a.clickRate,
      'bounceRate': a.bounceRate,
      'sent': a.totalRecipients,
    }).toList();
  }

  Future<List<Map<String, dynamic>>> getContactEngagementData(String period) async {
    final analytics = await getContactAnalytics(period);
    return analytics.map((a) => {
      'email': a.email,
      'name': a.name,
      'engagementScore': a.engagementScore,
      'emailsOpened': a.emailsOpened,
      'emailsClicked': a.emailsClicked,
    }).toList();
  }

  Future<Map<String, dynamic>> getDeliveryStatistics(String period) async {
    final overview = await getOverviewMetrics(period);
    final campaigns = await getCampaignAnalytics(period);
    
    int totalDelivered = 0;
    int totalBounced = 0;
    
    for (final campaign in campaigns) {
      totalDelivered += campaign.delivered;
      totalBounced += campaign.bounced;
    }
    
    final totalSent = overview['totalEmailsSent'] ?? 0;
    
    return {
      'totalSent': totalSent,
      'totalDelivered': totalDelivered,
      'totalBounced': totalBounced,
      'deliveryRate': totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0.0,
      'bounceRate': totalSent > 0 ? (totalBounced / totalSent) * 100 : 0.0,
    };
  }

  // Helper methods
  List<dynamic> _filterByPeriod(List<dynamic> items, String period) {
    final now = DateTime.now();
    DateTime cutoffDate;
    
    switch (period) {
      case '7d':
        cutoffDate = now.subtract(const Duration(days: 7));
        break;
      case '30d':
        cutoffDate = now.subtract(const Duration(days: 30));
        break;
      case '90d':
        cutoffDate = now.subtract(const Duration(days: 90));
        break;
      case '1y':
        cutoffDate = now.subtract(const Duration(days: 365));
        break;
      default:
        cutoffDate = now.subtract(const Duration(days: 30));
    }
    
    return items.where((item) {
      final date = item.scheduledAt ?? item.createdAt;
      return date.isAfter(cutoffDate);
    }).toList();
  }

  String _getPreviousPeriod(String period) {
    switch (period) {
      case '7d':
        return '14d';
      case '30d':
        return '60d';
      case '90d':
        return '180d';
      case '1y':
        return '2y';
      default:
        return '60d';
    }
  }

  double _calculateTrend(dynamic current, dynamic previous) {
    if (previous == 0) return 0.0;
    return ((current - previous) / previous) * 100;
  }

  double _calculateEngagementScore(Map<String, dynamic> engagement) {
    final received = engagement['received'] ?? 0;
    final opened = engagement['opened'] ?? 0;
    final clicked = engagement['clicked'] ?? 0;
    
    if (received == 0) return 0.0;
    
    final openRate = opened / received;
    final clickRate = clicked / received;
    
    return (openRate * 0.6 + clickRate * 0.4) * 100;
  }

  String _generateCSVReport(Map<String, dynamic> data) {
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('Email Marketing Report - ${data['period']}');
    buffer.writeln('Generated: ${data['generatedAt']}');
    buffer.writeln('');
    
    // Overview
    buffer.writeln('OVERVIEW');
    final overview = data['overview'];
    buffer.writeln('Total Campaigns,${overview['totalCampaigns']}');
    buffer.writeln('Total Emails Sent,${overview['totalEmailsSent']}');
    buffer.writeln('Average Open Rate,${overview['averageOpenRate'].toStringAsFixed(2)}%');
    buffer.writeln('Average Click Rate,${overview['averageClickRate'].toStringAsFixed(2)}%');
    buffer.writeln('');
    
    // Campaign details
    buffer.writeln('CAMPAIGN DETAILS');
    buffer.writeln('Name,Sent Date,Recipients,Delivered,Opened,Clicked,Open Rate,Click Rate');
    
    for (final campaign in data['campaigns']) {
      buffer.writeln('${campaign['name']},${campaign['sentDate']},${campaign['totalRecipients']},${campaign['delivered']},${campaign['opened']},${campaign['clicked']},${campaign['openRate'].toStringAsFixed(2)}%,${campaign['clickRate'].toStringAsFixed(2)}%');
    }
    
    return buffer.toString();
  }
}
