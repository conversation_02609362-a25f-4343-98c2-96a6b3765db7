// lib/providers/template_provider.dart
import 'package:flutter/foundation.dart';
import '../models/email_template_model.dart';
import '../models/recipient_model.dart';
import '../services/template_service.dart';

class TemplateProvider extends ChangeNotifier {
  final TemplateService _templateService = TemplateService();

  List<EmailTemplate> _templates = [];
  EmailTemplate? _selectedTemplate;
  RenderedTemplate? _previewTemplate;
  TemplateStats? _stats;
  List<String> _categories = [];
  bool _isLoading = false;
  String? _error;

  // Pagination
  int _currentPage = 0;
  int _pageSize = 20;
  int _totalCount = 0;
  bool _hasMore = true;

  // Filters
  String? _searchQuery;
  String? _selectedCategory;
  bool? _activeFilter;

  // Getters
  List<EmailTemplate> get templates => _templates;
  EmailTemplate? get selectedTemplate => _selectedTemplate;
  RenderedTemplate? get previewTemplate => _previewTemplate;
  TemplateStats? get stats => _stats;
  List<String> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get totalCount => _totalCount;
  bool get hasMore => _hasMore;
  int get currentPage => _currentPage;
  String? get searchQuery => _searchQuery;
  String? get selectedCategory => _selectedCategory;
  bool? get activeFilter => _activeFilter;

  // Initialize templates
  Future<void> initialize() async {
    await loadTemplates(refresh: true);
    await loadCategories();
    await loadStats();
  }

  // Load templates with pagination
  Future<void> loadTemplates({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _templates.clear();
      _hasMore = true;
    }

    if (!_hasMore || _isLoading) return;

    _setLoading(true);
    try {
      final offset = _currentPage * _pageSize;
      final newTemplates = await _templateService.getTemplates(
        limit: _pageSize,
        offset: offset,
        searchQuery: _searchQuery,
        category: _selectedCategory,
        isActive: _activeFilter,
      );

      final count = await _templateService.getTemplateCount(
        searchQuery: _searchQuery,
        category: _selectedCategory,
        isActive: _activeFilter,
      );

      if (refresh) {
        _templates = newTemplates;
      } else {
        _templates.addAll(newTemplates);
      }

      _totalCount = count;
      _hasMore = newTemplates.length == _pageSize;
      _currentPage++;
      _clearError();
    } catch (e) {
      _setError('Failed to load templates: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load more templates (pagination)
  Future<void> loadMoreTemplates() async {
    await loadTemplates();
  }

  // Search templates
  Future<void> searchTemplates(String query) async {
    _searchQuery = query.isEmpty ? null : query;
    await loadTemplates(refresh: true);
  }

  // Filter by category
  Future<void> filterByCategory(String? category) async {
    _selectedCategory = category;
    await loadTemplates(refresh: true);
  }

  // Filter by active status
  Future<void> filterByActive(bool? isActive) async {
    _activeFilter = isActive;
    await loadTemplates(refresh: true);
  }

  // Clear all filters
  Future<void> clearFilters() async {
    _searchQuery = null;
    _selectedCategory = null;
    _activeFilter = null;
    await loadTemplates(refresh: true);
  }

  // Create new template
  Future<bool> createTemplate(EmailTemplate template) async {
    _setLoading(true);
    try {
      final id = await _templateService.createTemplate(template);
      final createdTemplate = await _templateService.getTemplate(id);
      
      if (createdTemplate != null) {
        _templates.insert(0, createdTemplate);
        _totalCount++;
        await loadStats(); // Refresh stats
        _clearError();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to create template: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update template
  Future<bool> updateTemplate(EmailTemplate template) async {
    _setLoading(true);
    try {
      await _templateService.updateTemplate(template);
      
      // Update in local list
      final index = _templates.indexWhere((t) => t.id == template.id);
      if (index != -1) {
        _templates[index] = template;
      }
      
      // Update selected template if it's the same
      if (_selectedTemplate?.id == template.id) {
        _selectedTemplate = template;
      }
      
      await loadStats(); // Refresh stats
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update template: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete template
  Future<bool> deleteTemplate(String id) async {
    _setLoading(true);
    try {
      await _templateService.deleteTemplate(id);
      
      // Remove from local list
      _templates.removeWhere((t) => t.id == id);
      _totalCount--;
      
      // Clear selected template if it's the deleted one
      if (_selectedTemplate?.id == id) {
        _selectedTemplate = null;
      }
      
      await loadStats(); // Refresh stats
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete template: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Duplicate template
  Future<bool> duplicateTemplate(String id, String newName) async {
    _setLoading(true);
    try {
      final duplicated = await _templateService.duplicateTemplate(id, newName);
      
      if (duplicated != null) {
        _templates.insert(0, duplicated);
        _totalCount++;
        await loadStats(); // Refresh stats
        _clearError();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to duplicate template: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Select template
  void selectTemplate(EmailTemplate? template) {
    _selectedTemplate = template;
    _previewTemplate = null; // Clear preview when selecting new template
    notifyListeners();
  }

  // Preview template
  void previewTemplate(EmailTemplate template, {Map<String, dynamic>? sampleData}) {
    try {
      _previewTemplate = _templateService.previewTemplate(template, sampleData: sampleData);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to preview template: ${e.toString()}');
    }
  }

  // Render template for recipient
  RenderedTemplate? renderTemplateForRecipient(EmailTemplate template, Recipient recipient, {Map<String, dynamic>? additionalData}) {
    try {
      return _templateService.renderTemplate(template, recipient, additionalData: additionalData);
    } catch (e) {
      _setError('Failed to render template: ${e.toString()}');
      return null;
    }
  }

  // Validate template
  TemplateValidationResult validateTemplate(EmailTemplate template) {
    return _templateService.validateTemplate(template);
  }

  // Load categories
  Future<void> loadCategories() async {
    try {
      _categories = await _templateService.getCategories();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load categories: ${e.toString()}');
    }
  }

  // Load statistics
  Future<void> loadStats() async {
    try {
      _stats = await _templateService.getTemplateStats();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load template statistics: ${e.toString()}');
    }
  }

  // Get template by ID
  Future<EmailTemplate?> getTemplate(String id) async {
    try {
      // First check if it's already in the list
      final existing = _templates.firstWhere(
        (t) => t.id == id,
        orElse: () => EmailTemplate(name: '', subject: '', htmlContent: ''),
      );
      
      if (existing.id != null) {
        return existing;
      }
      
      // If not found, fetch from service
      return await _templateService.getTemplate(id);
    } catch (e) {
      _setError('Failed to get template: ${e.toString()}');
      return null;
    }
  }

  // Get active templates
  Future<List<EmailTemplate>> getActiveTemplates() async {
    try {
      return await _templateService.getActiveTemplates();
    } catch (e) {
      _setError('Failed to get active templates: ${e.toString()}');
      return [];
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _templates.clear();
    _selectedTemplate = null;
    _previewTemplate = null;
    _stats = null;
    _categories.clear();
    _currentPage = 0;
    _totalCount = 0;
    _hasMore = true;
    _searchQuery = null;
    _selectedCategory = null;
    _activeFilter = null;
    _clearError();
    notifyListeners();
  }
}
