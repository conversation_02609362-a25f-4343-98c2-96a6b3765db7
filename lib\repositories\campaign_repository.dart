// lib/repositories/campaign_repository.dart
import '../models/email_campaign.dart';
import '../services/database_service.dart';

class CampaignRepository {
  final DatabaseService _databaseService = DatabaseService();

  Future<void> createCampaign(EmailCampaign campaign) async {
    final db = await _databaseService.database;
    await db.insert('campaigns', campaign.toMap());
  }

  Future<EmailCampaign?> getCampaignById(String id) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'campaigns',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return EmailCampaign.fromMap(maps.first);
    }
    return null;
  }

  Future<List<EmailCampaign>> getAllCampaigns() async {
    final db = await _databaseService.database;
    final maps = await db.query('campaigns', orderBy: 'created_at DESC');
    return maps.map((map) => EmailCampaign.fromMap(map)).toList();
  }

  Future<List<EmailCampaign>> getCampaignsByStatus(CampaignStatus status) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'campaigns',
      where: 'status = ?',
      whereArgs: [status.toString()],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => EmailCampaign.fromMap(map)).toList();
  }

  Future<void> updateCampaign(EmailCampaign campaign) async {
    final db = await _databaseService.database;
    await db.update(
      'campaigns',
      campaign.toMap(),
      where: 'id = ?',
      whereArgs: [campaign.id],
    );
  }

  Future<void> deleteCampaign(String id) async {
    final db = await _databaseService.database;
    await db.delete(
      'campaigns',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<EmailCampaign>> getCampaignsForContact(String contactId) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'campaigns',
      where: 'recipient_ids LIKE ?',
      whereArgs: ['%$contactId%'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => EmailCampaign.fromMap(map)).toList();
  }

  Future<void> removeContactFromAllCampaigns(String contactId) async {
    final campaigns = await getCampaignsForContact(contactId);
    for (final campaign in campaigns) {
      final updatedRecipients = campaign.recipientIds
          .where((id) => id != contactId)
          .toList();
      
      final updatedCampaign = campaign.copyWith(recipientIds: updatedRecipients);
      await updateCampaign(updatedCampaign);
    }
  }

  Future<List<EmailCampaign>> searchCampaigns(String query) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'campaigns',
      where: 'name LIKE ? OR subject LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => EmailCampaign.fromMap(map)).toList();
  }

  Future<int> getCampaignCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM campaigns');
    return result.first['count'] as int;
  }

  Future<int> getCampaignCountByStatus(CampaignStatus status) async {
    final db = await _databaseService.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM campaigns WHERE status = ?',
      [status.toString()],
    );
    return result.first['count'] as int;
  }

  Future<List<EmailCampaign>> getRecentCampaigns(int limit) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'campaigns',
      orderBy: 'created_at DESC',
      limit: limit,
    );
    return maps.map((map) => EmailCampaign.fromMap(map)).toList();
  }

  Future<List<EmailCampaign>> getScheduledCampaigns() async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'campaigns',
      where: 'status = ? AND scheduled_at > ?',
      whereArgs: [CampaignStatus.scheduled.toString(), DateTime.now().toIso8601String()],
      orderBy: 'scheduled_at ASC',
    );
    return maps.map((map) => EmailCampaign.fromMap(map)).toList();
  }

  Future<Map<String, dynamic>> getCampaignStats(String campaignId) async {
    // This would typically query email tracking data
    // For now, return mock data
    return {
      'sent': 100,
      'delivered': 95,
      'opened': 45,
      'clicked': 12,
      'bounced': 5,
      'unsubscribed': 2,
    };
  }
}
