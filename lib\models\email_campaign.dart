// lib/models/email_campaign.dart
import 'package:uuid/uuid.dart';

enum CampaignStatus {
  draft,
  scheduled,
  sending,
  sent,
  paused,
  cancelled,
  completed,
}

enum CampaignType {
  regular,
  automated,
  drip,
  newsletter,
  promotional,
  transactional,
}

class EmailCampaign {
  final String id;
  final String name;
  final String subject;
  final String content;
  final String? senderEmail;
  final String? senderName;
  final String templateId;
  final String? templateName;
  final List<String> recipientIds;
  final CampaignStatus status;
  final CampaignType type;
  final DateTime? scheduledAt;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? sentAt;
  final DateTime? completedAt;
  final String createdBy;
  final Map<String, dynamic> settings;
  final Map<String, dynamic> personalizationData;
  final int recipientCount;
  final int sentCount;
  final int deliveredCount;
  final int openCount;
  final int clickCount;
  final int bounceCount;
  final int unsubscribeCount;
  final double progress;
  final String? errorMessage;
  final bool isRecurring;
  final String? recurringPattern;
  final DateTime? nextRunAt;

  EmailCampaign({
    String? id,
    required this.name,
    required this.subject,
    required this.content,
    this.senderEmail,
    this.senderName,
    required this.templateId,
    this.templateName,
    List<String>? recipientIds,
    this.status = CampaignStatus.draft,
    this.type = CampaignType.regular,
    this.scheduledAt,
    DateTime? createdAt,
    this.updatedAt,
    this.sentAt,
    this.completedAt,
    required this.createdBy,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? personalizationData,
    this.recipientCount = 0,
    this.sentCount = 0,
    this.deliveredCount = 0,
    this.openCount = 0,
    this.clickCount = 0,
    this.bounceCount = 0,
    this.unsubscribeCount = 0,
    this.progress = 0.0,
    this.errorMessage,
    this.isRecurring = false,
    this.recurringPattern,
    this.nextRunAt,
  }) : id = id ?? const Uuid().v4(),
       recipientIds = recipientIds ?? [],
       createdAt = createdAt ?? DateTime.now(),
       settings = settings ?? {},
       personalizationData = personalizationData ?? {};

  EmailCampaign copyWith({
    String? id,
    String? name,
    String? subject,
    String? content,
    String? senderEmail,
    String? senderName,
    String? templateId,
    String? templateName,
    List<String>? recipientIds,
    CampaignStatus? status,
    CampaignType? type,
    DateTime? scheduledAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? sentAt,
    DateTime? completedAt,
    String? createdBy,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? personalizationData,
    int? recipientCount,
    int? sentCount,
    int? deliveredCount,
    int? openCount,
    int? clickCount,
    int? bounceCount,
    int? unsubscribeCount,
    double? progress,
    String? errorMessage,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? nextRunAt,
  }) {
    return EmailCampaign(
      id: id ?? this.id,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      content: content ?? this.content,
      senderEmail: senderEmail ?? this.senderEmail,
      senderName: senderName ?? this.senderName,
      templateId: templateId ?? this.templateId,
      templateName: templateName ?? this.templateName,
      recipientIds: recipientIds ?? this.recipientIds,
      status: status ?? this.status,
      type: type ?? this.type,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sentAt: sentAt ?? this.sentAt,
      completedAt: completedAt ?? this.completedAt,
      createdBy: createdBy ?? this.createdBy,
      settings: settings ?? this.settings,
      personalizationData: personalizationData ?? this.personalizationData,
      recipientCount: recipientCount ?? this.recipientCount,
      sentCount: sentCount ?? this.sentCount,
      deliveredCount: deliveredCount ?? this.deliveredCount,
      openCount: openCount ?? this.openCount,
      clickCount: clickCount ?? this.clickCount,
      bounceCount: bounceCount ?? this.bounceCount,
      unsubscribeCount: unsubscribeCount ?? this.unsubscribeCount,
      progress: progress ?? this.progress,
      errorMessage: errorMessage ?? this.errorMessage,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      nextRunAt: nextRunAt ?? this.nextRunAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'content': content,
      'sender_email': senderEmail,
      'sender_name': senderName,
      'template_id': templateId,
      'template_name': templateName,
      'recipient_ids': recipientIds.join(','),
      'status': status.toString(),
      'type': type.toString(),
      'scheduled_at': scheduledAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sent_at': sentAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'created_by': createdBy,
      'settings':
          settings.isNotEmpty
              ? settings.entries.map((e) => '${e.key}:${e.value}').join('|')
              : null,
      'personalization_data':
          personalizationData.isNotEmpty
              ? personalizationData.entries
                  .map((e) => '${e.key}:${e.value}')
                  .join('|')
              : null,
      'recipient_count': recipientCount,
      'sent_count': sentCount,
      'delivered_count': deliveredCount,
      'open_count': openCount,
      'click_count': clickCount,
      'bounce_count': bounceCount,
      'unsubscribe_count': unsubscribeCount,
      'progress': progress,
      'error_message': errorMessage,
      'is_recurring': isRecurring ? 1 : 0,
      'recurring_pattern': recurringPattern,
      'next_run_at': nextRunAt?.toIso8601String(),
    };
  }

  factory EmailCampaign.fromMap(Map<String, dynamic> map) {
    final settingsStr = map['settings'] as String?;
    final settings = <String, dynamic>{};

    if (settingsStr != null && settingsStr.isNotEmpty) {
      for (final pair in settingsStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          settings[parts[0]] = parts[1];
        }
      }
    }

    final personalizationStr = map['personalization_data'] as String?;
    final personalizationData = <String, dynamic>{};

    if (personalizationStr != null && personalizationStr.isNotEmpty) {
      for (final pair in personalizationStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          personalizationData[parts[0]] = parts[1];
        }
      }
    }

    return EmailCampaign(
      id: map['id'] as String,
      name: map['name'] as String,
      subject: map['subject'] as String,
      content: map['content'] as String? ?? '',
      senderEmail: map['sender_email'] as String?,
      senderName: map['sender_name'] as String?,
      templateId: map['template_id'] as String,
      templateName: map['template_name'] as String?,
      recipientIds:
          (map['recipient_ids'] as String?)
              ?.split(',')
              .where((id) => id.isNotEmpty)
              .toList() ??
          [],
      status: CampaignStatus.values.firstWhere(
        (s) => s.toString() == map['status'],
        orElse: () => CampaignStatus.draft,
      ),
      type: CampaignType.values.firstWhere(
        (t) => t.toString() == map['type'],
        orElse: () => CampaignType.regular,
      ),
      scheduledAt:
          map['scheduled_at'] != null
              ? DateTime.parse(map['scheduled_at'] as String)
              : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt:
          map['updated_at'] != null
              ? DateTime.parse(map['updated_at'] as String)
              : null,
      sentAt:
          map['sent_at'] != null
              ? DateTime.parse(map['sent_at'] as String)
              : null,
      completedAt:
          map['completed_at'] != null
              ? DateTime.parse(map['completed_at'] as String)
              : null,
      createdBy: map['created_by'] as String,
      settings: settings,
      personalizationData: personalizationData,
      recipientCount: map['recipient_count'] as int? ?? 0,
      sentCount: map['sent_count'] as int? ?? 0,
      deliveredCount: map['delivered_count'] as int? ?? 0,
      openCount: map['open_count'] as int? ?? 0,
      clickCount: map['click_count'] as int? ?? 0,
      bounceCount: map['bounce_count'] as int? ?? 0,
      unsubscribeCount: map['unsubscribe_count'] as int? ?? 0,
      progress: (map['progress'] as num?)?.toDouble() ?? 0.0,
      errorMessage: map['error_message'] as String?,
      isRecurring: (map['is_recurring'] as int?) == 1,
      recurringPattern: map['recurring_pattern'] as String?,
      nextRunAt:
          map['next_run_at'] != null
              ? DateTime.parse(map['next_run_at'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory EmailCampaign.fromJson(Map<String, dynamic> json) =>
      EmailCampaign.fromMap(json);

  @override
  String toString() {
    return 'EmailCampaign(id: $id, name: $name, status: $status, recipientCount: $recipientCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmailCampaign && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  bool get isDraft => status == CampaignStatus.draft;
  bool get isScheduled => status == CampaignStatus.scheduled;
  bool get isSending => status == CampaignStatus.sending;
  bool get isSent => status == CampaignStatus.sent;
  bool get isCompleted => status == CampaignStatus.completed;
  bool get isPaused => status == CampaignStatus.paused;
  bool get isCancelled => status == CampaignStatus.cancelled;

  bool get canEdit => isDraft || isPaused;
  bool get canStart => isDraft || isPaused;
  bool get canPause => isSending;
  bool get canCancel => isDraft || isScheduled || isSending || isPaused;

  double get openRate {
    if (sentCount == 0) return 0.0;
    return (openCount / sentCount) * 100;
  }

  double get clickRate {
    if (sentCount == 0) return 0.0;
    return (clickCount / sentCount) * 100;
  }

  double get bounceRate {
    if (sentCount == 0) return 0.0;
    return (bounceCount / sentCount) * 100;
  }

  double get deliveryRate {
    if (sentCount == 0) return 0.0;
    return (deliveredCount / sentCount) * 100;
  }

  String get statusDisplayName {
    switch (status) {
      case CampaignStatus.draft:
        return 'Draft';
      case CampaignStatus.scheduled:
        return 'Scheduled';
      case CampaignStatus.sending:
        return 'Sending';
      case CampaignStatus.sent:
        return 'Sent';
      case CampaignStatus.paused:
        return 'Paused';
      case CampaignStatus.cancelled:
        return 'Cancelled';
      case CampaignStatus.completed:
        return 'Completed';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case CampaignType.regular:
        return 'Regular';
      case CampaignType.automated:
        return 'Automated';
      case CampaignType.drip:
        return 'Drip Campaign';
      case CampaignType.newsletter:
        return 'Newsletter';
      case CampaignType.promotional:
        return 'Promotional';
      case CampaignType.transactional:
        return 'Transactional';
    }
  }
}
