// lib/screens/contacts/import_contacts_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../../providers/contact_provider.dart';

class ImportContactsScreen extends StatefulWidget {
  const ImportContactsScreen({super.key});

  @override
  State<ImportContactsScreen> createState() => _ImportContactsScreenState();
}

class _ImportContactsScreenState extends State<ImportContactsScreen> {
  String? _selectedFilePath;
  bool _isImporting = false;
  List<Map<String, dynamic>>? _previewData;
  Map<String, String> _fieldMapping = {};
  final List<String> _requiredFields = ['name', 'email'];
  final List<String> _optionalFields = [
    'phone',
    'company',
    'position',
    'website',
    'address',
    'notes',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Import Contacts'),
        actions: [
          if (_previewData != null && _fieldMapping.isNotEmpty)
            TextButton(
              onPressed: _isImporting ? null : _importContacts,
              child:
                  _isImporting
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Import'),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Instructions
            _buildInstructions(),
            const SizedBox(height: 24),

            // File Selection
            _buildFileSelection(),
            const SizedBox(height: 24),

            // Field Mapping (if file selected)
            if (_previewData != null) ...[
              _buildFieldMapping(),
              const SizedBox(height: 24),
            ],

            // Preview (if mapping done)
            if (_previewData != null && _fieldMapping.isNotEmpty) ...[
              _buildPreview(),
              const SizedBox(height: 24),
            ],

            // Import Button
            if (_previewData != null && _fieldMapping.isNotEmpty)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isImporting ? null : _importContacts,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child:
                      _isImporting
                          ? const CircularProgressIndicator()
                          : Text('Import ${_previewData!.length} Contacts'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Import Instructions',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Follow these steps to import your contacts:',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 12),
            _buildInstructionStep(
              '1',
              'Prepare your CSV file with contact information',
            ),
            _buildInstructionStep(
              '2',
              'Select the CSV file using the button below',
            ),
            _buildInstructionStep('3', 'Map the columns to the correct fields'),
            _buildInstructionStep('4', 'Preview and confirm the import'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Required Fields:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text('• Name (required)'),
                  Text('• Email (required)'),
                  const SizedBox(height: 8),
                  Text(
                    'Optional Fields:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text('• Phone, Company, Position, Website, Address, Notes'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep(String number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  Widget _buildFileSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select CSV File',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (_selectedFilePath == null)
              Center(
                child: Column(
                  children: [
                    Icon(Icons.upload_file, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _selectFile,
                      icon: const Icon(Icons.file_upload),
                      label: const Text('Choose CSV File'),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Supported format: CSV (.csv)',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              )
            else
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'File Selected',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            _selectedFilePath!.split('/').last,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          if (_previewData != null)
                            Text(
                              '${_previewData!.length} rows found',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.green),
                            ),
                        ],
                      ),
                    ),
                    TextButton(
                      onPressed: _selectFile,
                      child: const Text('Change'),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFieldMapping() {
    if (_previewData == null || _previewData!.isEmpty) return const SizedBox();

    final csvHeaders = _previewData!.first.keys.toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Map CSV Columns to Contact Fields',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Text(
              'Match your CSV columns with the contact fields:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),

            // Required fields
            Text(
              'Required Fields',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.red),
            ),
            const SizedBox(height: 8),
            ..._requiredFields.map(
              (field) => _buildFieldMappingRow(field, csvHeaders, true),
            ),

            const SizedBox(height: 16),

            // Optional fields
            Text(
              'Optional Fields',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            ..._optionalFields.map(
              (field) => _buildFieldMappingRow(field, csvHeaders, false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFieldMappingRow(
    String field,
    List<String> csvHeaders,
    bool required,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              field.toUpperCase(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: required ? Colors.red : Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _fieldMapping[field],
              decoration: InputDecoration(
                border: const OutlineInputBorder(),
                hintText:
                    required
                        ? 'Select column (required)'
                        : 'Select column (optional)',
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('-- Skip this field --'),
                ),
                ...csvHeaders.map(
                  (header) => DropdownMenuItem<String>(
                    value: header,
                    child: Text(header),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  if (value == null) {
                    _fieldMapping.remove(field);
                  } else {
                    _fieldMapping[field] = value;
                  }
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreview() {
    if (_previewData == null || _previewData!.isEmpty) return const SizedBox();

    final previewRows = _previewData!.take(5).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preview (First 5 rows)',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: [
                  const DataColumn(label: Text('Name')),
                  const DataColumn(label: Text('Email')),
                  const DataColumn(label: Text('Company')),
                  const DataColumn(label: Text('Phone')),
                ],
                rows:
                    previewRows.map((row) {
                      return DataRow(
                        cells: [
                          DataCell(Text(_getMappedValue(row, 'name') ?? 'N/A')),
                          DataCell(
                            Text(_getMappedValue(row, 'email') ?? 'N/A'),
                          ),
                          DataCell(
                            Text(_getMappedValue(row, 'company') ?? 'N/A'),
                          ),
                          DataCell(
                            Text(_getMappedValue(row, 'phone') ?? 'N/A'),
                          ),
                        ],
                      );
                    }).toList(),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This is a preview of how your contacts will be imported. '
                      'Review the mapping and click Import to proceed.',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String? _getMappedValue(Map<String, dynamic> row, String field) {
    final csvColumn = _fieldMapping[field];
    if (csvColumn == null) return null;
    return row[csvColumn]?.toString();
  }

  Future<void> _selectFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        setState(() {
          _selectedFilePath = result.files.single.path;
          _previewData = null;
          _fieldMapping.clear();
        });

        // Parse CSV and show preview
        await _parseCSV();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error selecting file: $e')));
      }
    }
  }

  Future<void> _parseCSV() async {
    if (_selectedFilePath == null) return;

    try {
      // TODO: Implement CSV parsing
      // For now, simulate parsing with dummy data
      setState(() {
        _previewData = [
          {
            'Name': 'John Doe',
            'Email': '<EMAIL>',
            'Company': 'Acme Inc',
            'Phone': '************',
          },
          {
            'Name': 'Jane Smith',
            'Email': '<EMAIL>',
            'Company': 'Tech Corp',
            'Phone': '************',
          },
          {
            'Name': 'Bob Johnson',
            'Email': '<EMAIL>',
            'Company': 'StartupXYZ',
            'Phone': '************',
          },
        ];
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Found ${_previewData!.length} contacts in CSV file'),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error parsing CSV: $e')));
      }
    }
  }

  Future<void> _importContacts() async {
    if (_previewData == null || _previewData!.isEmpty) return;

    // Validate required fields are mapped
    for (String field in _requiredFields) {
      if (!_fieldMapping.containsKey(field)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Please map the required field: ${field.toUpperCase()}',
            ),
          ),
        );
        return;
      }
    }

    setState(() {
      _isImporting = true;
    });

    try {
      await context.read<ContactProvider>().importContacts(_previewData!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Successfully imported ${_previewData!.length} contacts',
            ),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error importing contacts: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isImporting = false;
        });
      }
    }
  }
}
