// lib/database/repositories/campaigns_repository.dart
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../database_helper.dart';
import '../../models/campaign_model.dart';

class CampaignsRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  Future<String> create(Campaign campaign) async {
    final db = await _dbHelper.database;
    final id = campaign.id ?? _uuid.v4();
    
    final campaignWithId = campaign.copyWith(
      id: id,
      updatedAt: DateTime.now(),
    );

    await db.insert(
      'campaigns',
      {
        'id': campaignWithId.id,
        'name': campaignWithId.name,
        'description': campaignWithId.description,
        'type': campaignWithId.type.toString().split('.').last,
        'status': campaignWithId.status.toString().split('.').last,
        'template_id': campaignWithId.templateId,
        'recipient_groups': jsonEncode(campaignWithId.recipientGroups),
        'recipient_tags': json<PERSON>ncode(campaignWithId.recipientTags),
        'specific_recipients': json<PERSON>ncode(campaignWithId.specificRecipients),
        'scheduled_at': campaignWithId.scheduledAt?.toIso8601String(),
        'sent_at': campaignWithId.sentAt?.toIso8601String(),
        'settings': jsonEncode(campaignWithId.settings),
        'analytics': jsonEncode(campaignWithId.analytics.toJson()),
        'created_at': campaignWithId.createdAt.toIso8601String(),
        'updated_at': campaignWithId.updatedAt?.toIso8601String(),
        'from_name': campaignWithId.fromName,
        'from_email': campaignWithId.fromEmail,
        'reply_to_email': campaignWithId.replyToEmail,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    return id;
  }

  Future<Campaign?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'campaigns',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToCampaign(maps.first);
  }

  Future<List<Campaign>> getAll({
    int? limit,
    int? offset,
    String? searchQuery,
    CampaignStatus? status,
    CampaignType? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += 'name LIKE ? OR description LIKE ?';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
    }

    if (status != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'status = ?';
      whereArgs.add(status.toString().split('.').last);
    }

    if (type != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'type = ?';
      whereArgs.add(type.toString().split('.').last);
    }

    if (startDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'created_at >= ?';
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'created_at <= ?';
      whereArgs.add(endDate.toIso8601String());
    }

    final maps = await db.query(
      'campaigns',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map(_mapToCampaign).toList();
  }

  Future<int> getCount({
    String? searchQuery,
    CampaignStatus? status,
    CampaignType? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += 'name LIKE ? OR description LIKE ?';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%']);
    }

    if (status != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'status = ?';
      whereArgs.add(status.toString().split('.').last);
    }

    if (type != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'type = ?';
      whereArgs.add(type.toString().split('.').last);
    }

    if (startDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'created_at >= ?';
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'created_at <= ?';
      whereArgs.add(endDate.toIso8601String());
    }

    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM campaigns${whereClause.isNotEmpty ? ' WHERE $whereClause' : ''}',
      whereArgs.isNotEmpty ? whereArgs : null,
    );

    return result.first['count'] as int;
  }

  Future<List<Campaign>> getScheduled() async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'campaigns',
      where: 'status = ? AND scheduled_at IS NOT NULL AND scheduled_at <= ?',
      whereArgs: [
        CampaignStatus.scheduled.toString().split('.').last,
        DateTime.now().toIso8601String(),
      ],
      orderBy: 'scheduled_at ASC',
    );

    return maps.map(_mapToCampaign).toList();
  }

  Future<List<Campaign>> getByStatus(CampaignStatus status) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'campaigns',
      where: 'status = ?',
      whereArgs: [status.toString().split('.').last],
      orderBy: 'created_at DESC',
    );

    return maps.map(_mapToCampaign).toList();
  }

  Future<void> update(Campaign campaign) async {
    final db = await _dbHelper.database;
    
    final updatedCampaign = campaign.copyWith(updatedAt: DateTime.now());

    await db.update(
      'campaigns',
      {
        'name': updatedCampaign.name,
        'description': updatedCampaign.description,
        'type': updatedCampaign.type.toString().split('.').last,
        'status': updatedCampaign.status.toString().split('.').last,
        'template_id': updatedCampaign.templateId,
        'recipient_groups': jsonEncode(updatedCampaign.recipientGroups),
        'recipient_tags': jsonEncode(updatedCampaign.recipientTags),
        'specific_recipients': jsonEncode(updatedCampaign.specificRecipients),
        'scheduled_at': updatedCampaign.scheduledAt?.toIso8601String(),
        'sent_at': updatedCampaign.sentAt?.toIso8601String(),
        'settings': jsonEncode(updatedCampaign.settings),
        'analytics': jsonEncode(updatedCampaign.analytics.toJson()),
        'updated_at': updatedCampaign.updatedAt?.toIso8601String(),
        'from_name': updatedCampaign.fromName,
        'from_email': updatedCampaign.fromEmail,
        'reply_to_email': updatedCampaign.replyToEmail,
      },
      where: 'id = ?',
      whereArgs: [campaign.id],
    );
  }

  Future<void> updateStatus(String id, CampaignStatus status) async {
    final db = await _dbHelper.database;
    await db.update(
      'campaigns',
      {
        'status': status.toString().split('.').last,
        'updated_at': DateTime.now().toIso8601String(),
        if (status == CampaignStatus.sent) 'sent_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateAnalytics(String id, CampaignAnalytics analytics) async {
    final db = await _dbHelper.database;
    await db.update(
      'campaigns',
      {
        'analytics': jsonEncode(analytics.toJson()),
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'campaigns',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Campaign _mapToCampaign(Map<String, dynamic> map) {
    return Campaign(
      id: map['id'],
      name: map['name'],
      description: map['description'] ?? '',
      type: CampaignType.values.firstWhere(
        (e) => e.toString() == 'CampaignType.${map['type']}',
        orElse: () => CampaignType.oneTime,
      ),
      status: CampaignStatus.values.firstWhere(
        (e) => e.toString() == 'CampaignStatus.${map['status']}',
        orElse: () => CampaignStatus.draft,
      ),
      templateId: map['template_id'],
      recipientGroups: _parseJsonList(map['recipient_groups']),
      recipientTags: _parseJsonList(map['recipient_tags']),
      specificRecipients: _parseJsonList(map['specific_recipients']),
      scheduledAt: map['scheduled_at'] != null ? DateTime.parse(map['scheduled_at']) : null,
      sentAt: map['sent_at'] != null ? DateTime.parse(map['sent_at']) : null,
      settings: _parseJson(map['settings']),
      analytics: CampaignAnalytics.fromJson(_parseJson(map['analytics'])),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      fromName: map['from_name'],
      fromEmail: map['from_email'],
      replyToEmail: map['reply_to_email'],
    );
  }

  Map<String, dynamic> _parseJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return {};
    try {
      return Map<String, dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      return {};
    }
  }

  List<String> _parseJsonList(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return [];
    try {
      return List<String>.from(jsonDecode(jsonString));
    } catch (e) {
      return [];
    }
  }
}
