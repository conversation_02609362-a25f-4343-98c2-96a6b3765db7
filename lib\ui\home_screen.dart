// lib/ui/home_screen.dart
import 'dart:io';
import 'dart:ui' show Offset;
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../config/smtp_config.dart';
import '../services/csv_service.dart';
import '../services/pdf_service.dart';
import '../services/mail_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  File? _pdfFile;
  File? _csvFile;
  String log = '';
  bool isLoading = false;
  final smtpServerController = TextEditingController(text: 'smtp.guvnl.com');
  final portController = TextEditingController(text: '587');
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final testMailController = TextEditingController();
  String testMailStatus = '';

  Future<void> pickPdf() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
    );
    if (result != null) {
      setState(() {
        _pdfFile = File(result.files.single.path!);
      });
    }
  }

  Future<void> pickCsv() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['csv'],
    );
    if (result != null) {
      setState(() {
        _csvFile = File(result.files.single.path!);
      });
    }
  }

  Future<void> startProcess() async {
    if (_pdfFile == null ||
        _csvFile == null ||
        emailController.text.isEmpty ||
        passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Please select files and fill SMTP credentials"),
        ),
      );
      return;
    }

    setState(() {
      isLoading = true;
      log = 'Processing started...\n';
    });

    try {
      final recipients = await CsvService.parseCsv(_csvFile!);

      final pages = await PdfService.splitPdf(_pdfFile!, 'output');
      final smtp = SmtpConfig(
        smtpServer: smtpServerController.text,
        port: int.parse(portController.text),
        username: emailController.text,
        password: passwordController.text,
        useSsl: false,
      );

      for (int i = 0; i < recipients.length && i < pages.length; i++) {
        try {
          await MailService.sendMail(smtp, recipients[i].email, pages[i]);
          setState(() {
            log += '[✓] Sent page ${i + 1} to ${recipients[i].email}\n';
          });
        } catch (e) {
          setState(() {
            log += '[✗] Failed to send to ${recipients[i].email}: $e\n';
          });
        }
      }
    } catch (e) {
      setState(() {
        log += '[!] Unexpected error: $e\n';
      });
    }

    setState(() {
      isLoading = false;
      log += 'Process complete.\n';
    });
  }

  Future<void> sendTestMail() async {
    if (testMailController.text.isEmpty ||
        _pdfFile == null ||
        emailController.text.isEmpty ||
        passwordController.text.isEmpty) {
      setState(
        () =>
            testMailStatus =
                'Please select PDF and enter SMTP/email credentials',
      );
      return;
    }
    try {
      final smtp = SmtpConfig(
        smtpServer: smtpServerController.text,
        port: int.parse(portController.text),
        username: emailController.text,
        password: passwordController.text,
        useSsl: false,
      );

      final pages = await PdfService.splitPdf(_pdfFile!, 'output');
      final firstPage = pages.first;
      await MailService.sendMail(smtp, testMailController.text, firstPage);
      setState(() => testMailStatus = '✅ Test mail sent with 1st page of PDF');
    } catch (e) {
      setState(() => testMailStatus = '❌ Failed to send test mail: $e');
    }
  }

  Widget smtpForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "SMTP Configuration",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        TextField(
          controller: smtpServerController,
          decoration: const InputDecoration(labelText: 'SMTP Server'),
        ),
        TextField(
          controller: portController,
          decoration: const InputDecoration(labelText: 'Port'),
          keyboardType: TextInputType.number,
        ),
        TextField(
          controller: emailController,
          decoration: const InputDecoration(labelText: 'Email'),
        ),
        TextField(
          controller: passwordController,
          decoration: const InputDecoration(labelText: 'App Password'),
          obscureText: true,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: testMailController,
                decoration: const InputDecoration(labelText: 'Test Email'),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: sendTestMail,
              child: const Text('Send Test'),
            ),
          ],
        ),
        if (testMailStatus.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              testMailStatus,
              style: TextStyle(
                color:
                    testMailStatus.startsWith('✅') ? Colors.green : Colors.red,
              ),
            ),
          ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget fileSelectionRow(String label, File? file, VoidCallback onPressed) {
    return Row(
      children: [
        ElevatedButton(onPressed: onPressed, child: Text(label)),
        const SizedBox(width: 12),
        if (file != null)
          Text(
            file.path.split("/").last,
            style: const TextStyle(color: Colors.greenAccent),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('PDF Mailer')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            fileSelectionRow('Select PDF', _pdfFile, pickPdf),
            const SizedBox(height: 8),
            fileSelectionRow('Select CSV', _csvFile, pickCsv),
            const SizedBox(height: 20),
            smtpForm(),
            isLoading
                ? const Center(child: CircularProgressIndicator())
                : ElevatedButton.icon(
                  onPressed: startProcess,
                  icon: const Icon(Icons.send),
                  label: const Text('Start Sending'),
                ),
            const SizedBox(height: 20),
            const Text(
              'Log Output',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Container(
              height: 300,
              padding: const EdgeInsets.all(8),
              color: Colors.black87,
              child: SingleChildScrollView(
                child: Text(
                  log,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    color: Colors.greenAccent,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
