// lib/repositories/contact_repository.dart
import '../models/contact.dart';
import '../services/database_service.dart';
import '../services/compliance_service.dart';

class ContactRepository {
  final DatabaseService _databaseService = DatabaseService();

  Future<void> createContact(Contact contact) async {
    final db = await _databaseService.database;
    await db.insert('contacts', contact.toMap());
  }

  Future<Contact?> getContactById(String id) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'contacts',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Contact.fromMap(maps.first);
    }
    return null;
  }

  Future<Contact?> getContactByEmail(String email) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'contacts',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return Contact.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Contact>> getAllContacts() async {
    final db = await _databaseService.database;
    final maps = await db.query('contacts', orderBy: 'created_at DESC');
    return maps.map((map) => Contact.fromMap(map)).toList();
  }

  Future<List<Contact>> getSubscribedContacts() async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'contacts',
      where: 'is_subscribed = ?',
      whereArgs: [1],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => Contact.fromMap(map)).toList();
  }

  Future<void> updateContact(Contact contact) async {
    final db = await _databaseService.database;
    await db.update(
      'contacts',
      contact.toMap(),
      where: 'id = ?',
      whereArgs: [contact.id],
    );
  }

  Future<void> deleteContact(String id) async {
    final db = await _databaseService.database;
    await db.delete(
      'contacts',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<Contact>> searchContacts(String query) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'contacts',
      where: 'name LIKE ? OR email LIKE ? OR company LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => Contact.fromMap(map)).toList();
  }

  Future<List<Contact>> getContactsByGroup(String group) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'contacts',
      where: 'groups LIKE ?',
      whereArgs: ['%$group%'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => Contact.fromMap(map)).toList();
  }

  Future<List<Contact>> getContactsByTag(String tag) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'contacts',
      where: 'tags LIKE ?',
      whereArgs: ['%$tag%'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => Contact.fromMap(map)).toList();
  }

  Future<int> getContactCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM contacts');
    return result.first['count'] as int;
  }

  Future<int> getSubscribedContactCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM contacts WHERE is_subscribed = ?',
      [1],
    );
    return result.first['count'] as int;
  }

  Future<List<String>> getAllTags() async {
    final contacts = await getAllContacts();
    final allTags = <String>{};
    
    for (final contact in contacts) {
      allTags.addAll(contact.tags);
    }
    
    return allTags.toList()..sort();
  }

  Future<List<String>> getAllGroups() async {
    final contacts = await getAllContacts();
    final allGroups = <String>{};
    
    for (final contact in contacts) {
      allGroups.addAll(contact.groups);
    }
    
    return allGroups.toList()..sort();
  }

  Future<void> bulkImportContacts(List<Contact> contacts) async {
    final db = await _databaseService.database;
    final batch = db.batch();
    
    for (final contact in contacts) {
      batch.insert('contacts', contact.toMap());
    }
    
    await batch.commit();
  }

  Future<List<Contact>> getRecentContacts(int limit) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'contacts',
      orderBy: 'created_at DESC',
      limit: limit,
    );
    return maps.map((map) => Contact.fromMap(map)).toList();
  }

  // GDPR Compliance methods
  Future<void> addConsentRecord(ConsentRecord consent) async {
    final db = await _databaseService.database;
    await db.insert('consent_records', consent.toMap());
  }

  Future<List<ConsentRecord>> getConsentRecords(String contactId) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'consent_records',
      where: 'contact_id = ?',
      whereArgs: [contactId],
      orderBy: 'timestamp DESC',
    );
    return maps.map((map) => ConsentRecord.fromMap(map)).toList();
  }

  Future<void> deleteConsentRecords(String contactId) async {
    final db = await _databaseService.database;
    await db.delete(
      'consent_records',
      where: 'contact_id = ?',
      whereArgs: [contactId],
    );
  }

  Future<Map<String, dynamic>> exportContactData(String contactId) async {
    final contact = await getContactById(contactId);
    final consentRecords = await getConsentRecords(contactId);
    
    return {
      'contact': contact?.toMap(),
      'consentRecords': consentRecords.map((r) => r.toMap()).toList(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  Future<List<Contact>> getContactsWithCustomField(String fieldName, String fieldValue) async {
    final contacts = await getAllContacts();
    return contacts.where((contact) {
      return contact.customFields[fieldName] == fieldValue;
    }).toList();
  }

  Future<void> updateContactSubscriptionStatus(String contactId, bool isSubscribed) async {
    final contact = await getContactById(contactId);
    if (contact != null) {
      final updatedContact = contact.copyWith(
        isSubscribed: isSubscribed,
        unsubscribedAt: isSubscribed ? null : DateTime.now(),
      );
      await updateContact(updatedContact);
    }
  }

  Future<List<Contact>> getContactsByIds(List<String> ids) async {
    if (ids.isEmpty) return [];
    
    final db = await _databaseService.database;
    final placeholders = List.filled(ids.length, '?').join(',');
    final maps = await db.query(
      'contacts',
      where: 'id IN ($placeholders)',
      whereArgs: ids,
    );
    return maps.map((map) => Contact.fromMap(map)).toList();
  }
}
