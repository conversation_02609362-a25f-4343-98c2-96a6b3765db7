// lib/providers/campaign_provider.dart
import 'package:flutter/foundation.dart';
import '../models/email_campaign.dart';
import '../repositories/campaign_repository.dart';
import '../services/email_service.dart';
import '../services/analytics_service.dart';

class CampaignProvider with ChangeNotifier {
  final CampaignRepository _campaignRepository = CampaignRepository();
  final EmailService _emailService = EmailService();
  final AnalyticsService _analyticsService = AnalyticsService();

  List<EmailCampaign> _campaigns = [];
  EmailCampaign? _selectedCampaign;
  bool _isLoading = false;
  String? _error;
  bool _isSending = false;

  // Getters
  List<EmailCampaign> get campaigns => _campaigns;
  EmailCampaign? get selectedCampaign => _selectedCampaign;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isSending => _isSending;

  List<EmailCampaign> get draftCampaigns => 
      _campaigns.where((c) => c.status == CampaignStatus.draft).toList();
  
  List<EmailCampaign> get scheduledCampaigns => 
      _campaigns.where((c) => c.status == CampaignStatus.scheduled).toList();
  
  List<EmailCampaign> get activeCampaigns => 
      _campaigns.where((c) => c.status == CampaignStatus.sending).toList();
  
  List<EmailCampaign> get completedCampaigns => 
      _campaigns.where((c) => c.status == CampaignStatus.completed || c.status == CampaignStatus.sent).toList();

  // Load campaigns
  Future<void> loadCampaigns() async {
    _setLoading(true);
    _clearError();

    try {
      _campaigns = await _campaignRepository.getAllCampaigns();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load campaigns: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create campaign
  Future<bool> createCampaign(EmailCampaign campaign) async {
    _setLoading(true);
    _clearError();

    try {
      await _campaignRepository.createCampaign(campaign);
      await loadCampaigns();
      return true;
    } catch (e) {
      _setError('Failed to create campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update campaign
  Future<bool> updateCampaign(EmailCampaign campaign) async {
    _setLoading(true);
    _clearError();

    try {
      await _campaignRepository.updateCampaign(campaign);
      await loadCampaigns();
      
      // Update selected campaign if it's the same one
      if (_selectedCampaign?.id == campaign.id) {
        _selectedCampaign = campaign;
      }
      
      return true;
    } catch (e) {
      _setError('Failed to update campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete campaign
  Future<bool> deleteCampaign(String campaignId) async {
    _setLoading(true);
    _clearError();

    try {
      await _campaignRepository.deleteCampaign(campaignId);
      await loadCampaigns();
      
      // Clear selected campaign if it was deleted
      if (_selectedCampaign?.id == campaignId) {
        _selectedCampaign = null;
      }
      
      return true;
    } catch (e) {
      _setError('Failed to delete campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Select campaign
  void selectCampaign(EmailCampaign? campaign) {
    _selectedCampaign = campaign;
    notifyListeners();
  }

  // Send campaign
  Future<bool> sendCampaign(String campaignId) async {
    _isSending = true;
    _clearError();
    notifyListeners();

    try {
      final campaign = await _campaignRepository.getCampaignById(campaignId);
      if (campaign == null) {
        throw Exception('Campaign not found');
      }

      // Update campaign status to sending
      final sendingCampaign = campaign.copyWith(
        status: CampaignStatus.sending,
        sentAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await _campaignRepository.updateCampaign(sendingCampaign);

      // Send emails (this would be handled by a background service in production)
      final success = await _emailService.sendCampaign(campaign);
      
      if (success) {
        // Update campaign status to completed
        final completedCampaign = sendingCampaign.copyWith(
          status: CampaignStatus.completed,
          completedAt: DateTime.now(),
          progress: 100.0,
        );
        await _campaignRepository.updateCampaign(completedCampaign);
        
        // Record analytics event
        await _analyticsService.recordCampaignSent(campaignId);
      } else {
        // Update campaign with error
        final errorCampaign = sendingCampaign.copyWith(
          status: CampaignStatus.draft,
          errorMessage: 'Failed to send campaign',
        );
        await _campaignRepository.updateCampaign(errorCampaign);
      }

      await loadCampaigns();
      return success;
    } catch (e) {
      _setError('Failed to send campaign: $e');
      return false;
    } finally {
      _isSending = false;
      notifyListeners();
    }
  }

  // Schedule campaign
  Future<bool> scheduleCampaign(String campaignId, DateTime scheduledTime) async {
    _setLoading(true);
    _clearError();

    try {
      final campaign = await _campaignRepository.getCampaignById(campaignId);
      if (campaign == null) {
        throw Exception('Campaign not found');
      }

      final scheduledCampaign = campaign.copyWith(
        status: CampaignStatus.scheduled,
        scheduledAt: scheduledTime,
        updatedAt: DateTime.now(),
      );

      await _campaignRepository.updateCampaign(scheduledCampaign);
      await loadCampaigns();
      return true;
    } catch (e) {
      _setError('Failed to schedule campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Pause campaign
  Future<bool> pauseCampaign(String campaignId) async {
    _setLoading(true);
    _clearError();

    try {
      final campaign = await _campaignRepository.getCampaignById(campaignId);
      if (campaign == null) {
        throw Exception('Campaign not found');
      }

      final pausedCampaign = campaign.copyWith(
        status: CampaignStatus.paused,
        updatedAt: DateTime.now(),
      );

      await _campaignRepository.updateCampaign(pausedCampaign);
      await loadCampaigns();
      return true;
    } catch (e) {
      _setError('Failed to pause campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Resume campaign
  Future<bool> resumeCampaign(String campaignId) async {
    _setLoading(true);
    _clearError();

    try {
      final campaign = await _campaignRepository.getCampaignById(campaignId);
      if (campaign == null) {
        throw Exception('Campaign not found');
      }

      final resumedCampaign = campaign.copyWith(
        status: CampaignStatus.sending,
        updatedAt: DateTime.now(),
      );

      await _campaignRepository.updateCampaign(resumedCampaign);
      await loadCampaigns();
      return true;
    } catch (e) {
      _setError('Failed to resume campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cancel campaign
  Future<bool> cancelCampaign(String campaignId) async {
    _setLoading(true);
    _clearError();

    try {
      final campaign = await _campaignRepository.getCampaignById(campaignId);
      if (campaign == null) {
        throw Exception('Campaign not found');
      }

      final cancelledCampaign = campaign.copyWith(
        status: CampaignStatus.cancelled,
        updatedAt: DateTime.now(),
      );

      await _campaignRepository.updateCampaign(cancelledCampaign);
      await loadCampaigns();
      return true;
    } catch (e) {
      _setError('Failed to cancel campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Search campaigns
  Future<List<EmailCampaign>> searchCampaigns(String query) async {
    try {
      return await _campaignRepository.searchCampaigns(query);
    } catch (e) {
      _setError('Failed to search campaigns: $e');
      return [];
    }
  }

  // Get campaign statistics
  Future<Map<String, dynamic>?> getCampaignStats(String campaignId) async {
    try {
      return await _campaignRepository.getCampaignStats(campaignId);
    } catch (e) {
      _setError('Failed to get campaign stats: $e');
      return null;
    }
  }

  // Duplicate campaign
  Future<bool> duplicateCampaign(EmailCampaign campaign) async {
    _setLoading(true);
    _clearError();

    try {
      final duplicatedCampaign = campaign.copyWith(
        id: null, // This will generate a new ID
        name: '${campaign.name} (Copy)',
        status: CampaignStatus.draft,
        createdAt: DateTime.now(),
        updatedAt: null,
        sentAt: null,
        completedAt: null,
        sentCount: 0,
        deliveredCount: 0,
        openCount: 0,
        clickCount: 0,
        bounceCount: 0,
        unsubscribeCount: 0,
        progress: 0.0,
        errorMessage: null,
      );

      await _campaignRepository.createCampaign(duplicatedCampaign);
      await loadCampaigns();
      return true;
    } catch (e) {
      _setError('Failed to duplicate campaign: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Get campaigns by status
  Future<List<EmailCampaign>> getCampaignsByStatus(CampaignStatus status) async {
    try {
      return await _campaignRepository.getCampaignsByStatus(status);
    } catch (e) {
      _setError('Failed to get campaigns by status: $e');
      return [];
    }
  }

  // Get recent campaigns
  Future<List<EmailCampaign>> getRecentCampaigns(int limit) async {
    try {
      return await _campaignRepository.getRecentCampaigns(limit);
    } catch (e) {
      _setError('Failed to get recent campaigns: $e');
      return [];
    }
  }

  // Refresh campaigns
  Future<void> refresh() async {
    await loadCampaigns();
  }
}
