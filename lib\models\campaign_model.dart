// lib/models/campaign_model.dart

enum CampaignStatus {
  draft,
  scheduled,
  sending,
  sent,
  paused,
  cancelled,
  failed,
}

enum CampaignType { oneTime, recurring, autoResponder }

class Campaign {
  final String? id;
  final String name;
  final String description;
  final CampaignType type;
  final CampaignStatus status;
  final String templateId;
  final List<String> recipientGroups;
  final List<String> recipientTags;
  final List<String> specificRecipients; // Individual recipient IDs
  final DateTime? scheduledAt;
  final DateTime? sentAt;
  final Map<String, dynamic> settings;
  final CampaignAnalytics analytics;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? fromName;
  final String? fromEmail;
  final String? replyToEmail;

  Campaign({
    this.id,
    required this.name,
    this.description = '',
    this.type = CampaignType.oneTime,
    this.status = CampaignStatus.draft,
    required this.templateId,
    this.recipientGroups = const [],
    this.recipientTags = const [],
    this.specificRecipients = const [],
    this.scheduledAt,
    this.sentAt,
    this.settings = const {},
    CampaignAnalytics? analytics,
    DateTime? createdAt,
    this.updatedAt,
    this.fromName,
    this.fromEmail,
    this.replyToEmail,
  }) : analytics = analytics ?? CampaignAnalytics(),
       createdAt = createdAt ?? DateTime.now();

  factory Campaign.fromJson(Map<String, dynamic> json) {
    return Campaign(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      type: CampaignType.values.firstWhere(
        (e) => e.toString() == 'CampaignType.${json['type']}',
        orElse: () => CampaignType.oneTime,
      ),
      status: CampaignStatus.values.firstWhere(
        (e) => e.toString() == 'CampaignStatus.${json['status']}',
        orElse: () => CampaignStatus.draft,
      ),
      templateId: json['templateId'],
      recipientGroups: List<String>.from(json['recipientGroups'] ?? []),
      recipientTags: List<String>.from(json['recipientTags'] ?? []),
      specificRecipients: List<String>.from(json['specificRecipients'] ?? []),
      scheduledAt:
          json['scheduledAt'] != null
              ? DateTime.parse(json['scheduledAt'])
              : null,
      sentAt: json['sentAt'] != null ? DateTime.parse(json['sentAt']) : null,
      settings: Map<String, dynamic>.from(json['settings'] ?? {}),
      analytics: CampaignAnalytics.fromJson(json['analytics'] ?? {}),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      fromName: json['fromName'],
      fromEmail: json['fromEmail'],
      replyToEmail: json['replyToEmail'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'templateId': templateId,
      'recipientGroups': recipientGroups,
      'recipientTags': recipientTags,
      'specificRecipients': specificRecipients,
      'scheduledAt': scheduledAt?.toIso8601String(),
      'sentAt': sentAt?.toIso8601String(),
      'settings': settings,
      'analytics': analytics.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'fromName': fromName,
      'fromEmail': fromEmail,
      'replyToEmail': replyToEmail,
    };
  }

  Campaign copyWith({
    String? id,
    String? name,
    String? description,
    CampaignType? type,
    CampaignStatus? status,
    String? templateId,
    List<String>? recipientGroups,
    List<String>? recipientTags,
    List<String>? specificRecipients,
    DateTime? scheduledAt,
    DateTime? sentAt,
    Map<String, dynamic>? settings,
    CampaignAnalytics? analytics,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? fromName,
    String? fromEmail,
    String? replyToEmail,
  }) {
    return Campaign(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      templateId: templateId ?? this.templateId,
      recipientGroups: recipientGroups ?? this.recipientGroups,
      recipientTags: recipientTags ?? this.recipientTags,
      specificRecipients: specificRecipients ?? this.specificRecipients,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      settings: settings ?? this.settings,
      analytics: analytics ?? this.analytics,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      fromName: fromName ?? this.fromName,
      fromEmail: fromEmail ?? this.fromEmail,
      replyToEmail: replyToEmail ?? this.replyToEmail,
    );
  }
}

class CampaignAnalytics {
  final int totalRecipients;
  final int emailsSent;
  final int emailsDelivered;
  final int emailsBounced;
  final int emailsOpened;
  final int emailsClicked;
  final int unsubscribes;
  final int complaints;
  final DateTime? lastUpdated;

  CampaignAnalytics({
    this.totalRecipients = 0,
    this.emailsSent = 0,
    this.emailsDelivered = 0,
    this.emailsBounced = 0,
    this.emailsOpened = 0,
    this.emailsClicked = 0,
    this.unsubscribes = 0,
    this.complaints = 0,
    this.lastUpdated,
  });

  factory CampaignAnalytics.fromJson(Map<String, dynamic> json) {
    return CampaignAnalytics(
      totalRecipients: json['totalRecipients'] ?? 0,
      emailsSent: json['emailsSent'] ?? 0,
      emailsDelivered: json['emailsDelivered'] ?? 0,
      emailsBounced: json['emailsBounced'] ?? 0,
      emailsOpened: json['emailsOpened'] ?? 0,
      emailsClicked: json['emailsClicked'] ?? 0,
      unsubscribes: json['unsubscribes'] ?? 0,
      complaints: json['complaints'] ?? 0,
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.parse(json['lastUpdated'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalRecipients': totalRecipients,
      'emailsSent': emailsSent,
      'emailsDelivered': emailsDelivered,
      'emailsBounced': emailsBounced,
      'emailsOpened': emailsOpened,
      'emailsClicked': emailsClicked,
      'unsubscribes': unsubscribes,
      'complaints': complaints,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  CampaignAnalytics copyWith({
    int? totalRecipients,
    int? emailsSent,
    int? emailsDelivered,
    int? emailsBounced,
    int? emailsOpened,
    int? emailsClicked,
    int? unsubscribes,
    int? complaints,
    DateTime? lastUpdated,
    int? emailsFailed,
    DateTime? lastSentAt,
  }) {
    return CampaignAnalytics(
      totalRecipients: totalRecipients ?? this.totalRecipients,
      emailsSent: emailsSent ?? this.emailsSent,
      emailsDelivered: emailsDelivered ?? this.emailsDelivered,
      emailsBounced: emailsBounced ?? this.emailsBounced,
      emailsOpened: emailsOpened ?? this.emailsOpened,
      emailsClicked: emailsClicked ?? this.emailsClicked,
      unsubscribes: unsubscribes ?? this.unsubscribes,
      complaints: complaints ?? this.complaints,
      lastUpdated: lastUpdated ?? lastSentAt ?? this.lastUpdated,
    );
  }

  double get deliveryRate =>
      emailsSent > 0 ? (emailsDelivered / emailsSent) * 100 : 0;
  double get openRate =>
      emailsDelivered > 0 ? (emailsOpened / emailsDelivered) * 100 : 0;
  double get clickRate =>
      emailsOpened > 0 ? (emailsClicked / emailsOpened) * 100 : 0;
  double get bounceRate =>
      emailsSent > 0 ? (emailsBounced / emailsSent) * 100 : 0;
  double get unsubscribeRate =>
      emailsDelivered > 0 ? (unsubscribes / emailsDelivered) * 100 : 0;
}
