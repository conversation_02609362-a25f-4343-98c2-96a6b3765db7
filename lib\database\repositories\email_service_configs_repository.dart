// lib/database/repositories/email_service_configs_repository.dart
import '../../services/database_service.dart';
import '../../models/email_service_config.dart';

class EmailServiceConfigsRepository {
  final DatabaseService _databaseService = DatabaseService();

  Future<String> create(EmailServiceConfig config) async {
    final configMap = config.toMap();
    await _databaseService.insert('email_service_configs', configMap);
    return config.id;
  }

  Future<List<String>> createBatch(List<EmailServiceConfig> configs) async {
    final batch = await _databaseService.batch();
    final ids = <String>[];
    
    for (final config in configs) {
      batch.insert('email_service_configs', config.toMap());
      ids.add(config.id);
    }
    
    await batch.commit();
    return ids;
  }

  Future<EmailServiceConfig?> getById(String id) async {
    final results = await _databaseService.query(
      'email_service_configs',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (results.isNotEmpty) {
      return EmailServiceConfig.fromMap(results.first);
    }
    return null;
  }

  Future<List<EmailServiceConfig>> getAll() async {
    final results = await _databaseService.query(
      'email_service_configs',
      orderBy: 'created_at DESC',
    );

    return results.map((map) => EmailServiceConfig.fromMap(map)).toList();
  }

  Future<List<EmailServiceConfig>> getActive() async {
    final results = await _databaseService.query(
      'email_service_configs',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'created_at DESC',
    );

    return results.map((map) => EmailServiceConfig.fromMap(map)).toList();
  }

  Future<EmailServiceConfig?> getDefault() async {
    final results = await _databaseService.query(
      'email_service_configs',
      where: 'is_default = ? AND is_active = ?',
      whereArgs: [1, 1],
      limit: 1,
    );

    if (results.isNotEmpty) {
      return EmailServiceConfig.fromMap(results.first);
    }
    return null;
  }

  Future<List<EmailServiceConfig>> getByProvider(String provider) async {
    final results = await _databaseService.query(
      'email_service_configs',
      where: 'provider = ?',
      whereArgs: [provider],
      orderBy: 'created_at DESC',
    );

    return results.map((map) => EmailServiceConfig.fromMap(map)).toList();
  }

  Future<void> update(EmailServiceConfig config) async {
    await _databaseService.update(
      'email_service_configs',
      config.toMap(),
      where: 'id = ?',
      whereArgs: [config.id],
    );
  }

  Future<void> delete(String id) async {
    await _databaseService.delete(
      'email_service_configs',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> setAsDefault(String id) async {
    await _databaseService.transaction((txn) async {
      // Remove default flag from all configs
      await txn.update(
        'email_service_configs',
        {'is_default': 0},
        where: 'is_default = ?',
        whereArgs: [1],
      );

      // Set the specified config as default
      await txn.update(
        'email_service_configs',
        {'is_default': 1, 'is_active': 1},
        where: 'id = ?',
        whereArgs: [id],
      );
    });
  }

  Future<void> activate(String id) async {
    await _databaseService.update(
      'email_service_configs',
      {'is_active': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deactivate(String id) async {
    await _databaseService.update(
      'email_service_configs',
      {'is_active': 0, 'is_default': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<bool> exists(String id) async {
    final results = await _databaseService.query(
      'email_service_configs',
      columns: ['id'],
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    return results.isNotEmpty;
  }

  Future<bool> emailExists(String email) async {
    final results = await _databaseService.query(
      'email_service_configs',
      columns: ['id'],
      where: 'from_email = ?',
      whereArgs: [email],
      limit: 1,
    );

    return results.isNotEmpty;
  }

  Future<int> count() async {
    final result = await _databaseService.rawQuery(
      'SELECT COUNT(*) as count FROM email_service_configs',
    );
    return result.first['count'] as int;
  }

  Future<int> countActive() async {
    final result = await _databaseService.rawQuery(
      'SELECT COUNT(*) as count FROM email_service_configs WHERE is_active = ?',
      [1],
    );
    return result.first['count'] as int;
  }

  Future<int> countByProvider(String provider) async {
    final result = await _databaseService.rawQuery(
      'SELECT COUNT(*) as count FROM email_service_configs WHERE provider = ?',
      [provider],
    );
    return result.first['count'] as int;
  }

  Future<List<EmailServiceConfig>> search(String query) async {
    final results = await _databaseService.query(
      'email_service_configs',
      where: 'name LIKE ? OR from_email LIKE ? OR provider LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return results.map((map) => EmailServiceConfig.fromMap(map)).toList();
  }

  Future<void> deleteAll() async {
    await _databaseService.delete('email_service_configs');
  }

  Future<List<EmailServiceConfig>> getPaginated({
    int offset = 0,
    int limit = 50,
    String? orderBy,
  }) async {
    final results = await _databaseService.query(
      'email_service_configs',
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return results.map((map) => EmailServiceConfig.fromMap(map)).toList();
  }

  Future<Map<String, dynamic>> getStats() async {
    final totalResult = await _databaseService.rawQuery(
      'SELECT COUNT(*) as total FROM email_service_configs',
    );
    
    final activeResult = await _databaseService.rawQuery(
      'SELECT COUNT(*) as active FROM email_service_configs WHERE is_active = ?',
      [1],
    );
    
    final defaultResult = await _databaseService.rawQuery(
      'SELECT COUNT(*) as default_count FROM email_service_configs WHERE is_default = ?',
      [1],
    );

    final providerStats = await _databaseService.rawQuery(
      'SELECT provider, COUNT(*) as count FROM email_service_configs GROUP BY provider',
    );

    return {
      'total': totalResult.first['total'] as int,
      'active': activeResult.first['active'] as int,
      'hasDefault': (defaultResult.first['default_count'] as int) > 0,
      'byProvider': Map.fromIterable(
        providerStats,
        key: (item) => item['provider'],
        value: (item) => item['count'],
      ),
    };
  }

  Future<void> updateBatch(List<EmailServiceConfig> configs) async {
    final batch = await _databaseService.batch();
    
    for (final config in configs) {
      batch.update(
        'email_service_configs',
        config.toMap(),
        where: 'id = ?',
        whereArgs: [config.id],
      );
    }
    
    await batch.commit();
  }

  Future<void> deleteBatch(List<String> ids) async {
    final batch = await _databaseService.batch();
    
    for (final id in ids) {
      batch.delete(
        'email_service_configs',
        where: 'id = ?',
        whereArgs: [id],
      );
    }
    
    await batch.commit();
  }

  Future<EmailServiceConfig?> getByName(String name) async {
    final results = await _databaseService.query(
      'email_service_configs',
      where: 'name = ?',
      whereArgs: [name],
      limit: 1,
    );

    if (results.isNotEmpty) {
      return EmailServiceConfig.fromMap(results.first);
    }
    return null;
  }

  Future<List<String>> getAllProviders() async {
    final results = await _databaseService.rawQuery(
      'SELECT DISTINCT provider FROM email_service_configs ORDER BY provider',
    );

    return results.map((row) => row['provider'] as String).toList();
  }

  Future<void> backup() async {
    // Implementation for backing up email service configs
    // This could export to JSON or another format
  }

  Future<void> restore(List<EmailServiceConfig> configs) async {
    await _databaseService.transaction((txn) async {
      // Clear existing configs
      await txn.delete('email_service_configs');
      
      // Insert restored configs
      for (final config in configs) {
        await txn.insert('email_service_configs', config.toMap());
      }
    });
  }
}
