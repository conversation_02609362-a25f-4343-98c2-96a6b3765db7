// lib/models/user.dart
import 'package:uuid/uuid.dart';

enum UserRole {
  admin,
  manager,
  user,
}

class User {
  final String id;
  final String email;
  final String name;
  final String? company;
  final UserRole role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic> preferences;
  final Map<String, dynamic> metadata;

  User({
    String? id,
    required this.email,
    required this.name,
    this.company,
    this.role = UserRole.user,
    this.isActive = true,
    DateTime? createdAt,
    this.updatedAt,
    this.lastLoginAt,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? metadata,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       preferences = preferences ?? {},
       metadata = metadata ?? {};

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? company,
    UserRole? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      company: company ?? this.company,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'company': company,
      'role': role.toString(),
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'preferences': preferences.isNotEmpty ? 
          preferences.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
      'metadata': metadata.isNotEmpty ? 
          metadata.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    final preferencesStr = map['preferences'] as String?;
    final preferences = <String, dynamic>{};
    
    if (preferencesStr != null && preferencesStr.isNotEmpty) {
      for (final pair in preferencesStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          preferences[parts[0]] = parts[1];
        }
      }
    }

    final metadataStr = map['metadata'] as String?;
    final metadata = <String, dynamic>{};
    
    if (metadataStr != null && metadataStr.isNotEmpty) {
      for (final pair in metadataStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          metadata[parts[0]] = parts[1];
        }
      }
    }

    return User(
      id: map['id'] as String,
      email: map['email'] as String,
      name: map['name'] as String,
      company: map['company'] as String?,
      role: UserRole.values.firstWhere(
        (r) => r.toString() == map['role'],
        orElse: () => UserRole.user,
      ),
      isActive: (map['is_active'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at'] as String) : null,
      lastLoginAt: map['last_login_at'] != null ? DateTime.parse(map['last_login_at'] as String) : null,
      preferences: preferences,
      metadata: metadata,
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory User.fromJson(Map<String, dynamic> json) => User.fromMap(json);

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  bool get isAdmin => role == UserRole.admin;
  bool get isManager => role == UserRole.manager;
  bool get canManageUsers => role == UserRole.admin;
  bool get canManageCampaigns => role == UserRole.admin || role == UserRole.manager;
  bool get canViewAnalytics => isActive;

  String get roleDisplayName {
    switch (role) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.manager:
        return 'Manager';
      case UserRole.user:
        return 'User';
    }
  }

  String get statusDisplayName => isActive ? 'Active' : 'Inactive';

  Duration? get timeSinceLastLogin {
    if (lastLoginAt != null) {
      return DateTime.now().difference(lastLoginAt!);
    }
    return null;
  }

  bool get hasLoggedInRecently {
    final timeSince = timeSinceLastLogin;
    return timeSince != null && timeSince.inDays <= 7;
  }

  User updateLastLogin() {
    return copyWith(lastLoginAt: DateTime.now());
  }

  User updatePreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences);
    newPreferences[key] = value;
    return copyWith(preferences: newPreferences);
  }

  User removePreference(String key) {
    final newPreferences = Map<String, dynamic>.from(preferences);
    newPreferences.remove(key);
    return copyWith(preferences: newPreferences);
  }

  User addMetadata(String key, dynamic value) {
    final newMetadata = Map<String, dynamic>.from(metadata);
    newMetadata[key] = value;
    return copyWith(metadata: newMetadata);
  }

  User removeMetadata(String key) {
    final newMetadata = Map<String, dynamic>.from(metadata);
    newMetadata.remove(key);
    return copyWith(metadata: newMetadata);
  }

  User activate() => copyWith(isActive: true, updatedAt: DateTime.now());
  User deactivate() => copyWith(isActive: false, updatedAt: DateTime.now());

  User promoteToManager() => copyWith(role: UserRole.manager, updatedAt: DateTime.now());
  User promoteToAdmin() => copyWith(role: UserRole.admin, updatedAt: DateTime.now());
  User demoteToUser() => copyWith(role: UserRole.user, updatedAt: DateTime.now());

  Map<String, dynamic> getSecurityInfo() {
    return {
      'id': id,
      'email': email,
      'role': role.toString(),
      'isActive': isActive,
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'hasLoggedInRecently': hasLoggedInRecently,
      'timeSinceLastLogin': timeSinceLastLogin?.inDays,
    };
  }

  Map<String, dynamic> getProfileInfo() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'company': company,
      'role': roleDisplayName,
      'status': statusDisplayName,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }
}
