// lib/services/security_service.dart
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  static const String _keyPrefix = 'enc_key_';
  static const String _ivPrefix = 'enc_iv_';

  // Generate a secure random key for encryption
  String generateSecureKey({int length = 32}) {
    final random = Random.secure();
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = random.nextInt(256);
    }
    return base64.encode(bytes);
  }

  // Generate initialization vector for encryption
  String generateIV({int length = 16}) {
    final random = Random.secure();
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = random.nextInt(256);
    }
    return base64.encode(bytes);
  }

  // Simple XOR encryption (for demonstration - use AES in production)
  String encryptData(String data, String key) {
    try {
      final dataBytes = utf8.encode(data);
      final keyBytes = base64.decode(key);
      final encrypted = <int>[];

      for (int i = 0; i < dataBytes.length; i++) {
        encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return base64.encode(encrypted);
    } catch (e) {
      throw SecurityException('Encryption failed: ${e.toString()}');
    }
  }

  // Simple XOR decryption (for demonstration - use AES in production)
  String decryptData(String encryptedData, String key) {
    try {
      final encryptedBytes = base64.decode(encryptedData);
      final keyBytes = base64.decode(key);
      final decrypted = <int>[];

      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return utf8.decode(decrypted);
    } catch (e) {
      throw SecurityException('Decryption failed: ${e.toString()}');
    }
  }

  // Hash sensitive data
  String hashData(String data, {String? salt}) {
    salt ??= generateSecureKey(length: 16);
    final bytes = utf8.encode(data + salt);
    final digest = sha256.convert(bytes);
    return '$salt:${digest.toString()}';
  }

  // Verify hashed data
  bool verifyHash(String data, String hash) {
    try {
      final parts = hash.split(':');
      if (parts.length != 2) return false;

      final salt = parts[0];
      final storedHash = parts[1];

      final bytes = utf8.encode(data + salt);
      final digest = sha256.convert(bytes);

      return digest.toString() == storedHash;
    } catch (e) {
      return false;
    }
  }

  // Store encrypted data in secure storage
  Future<void> storeSecureData(String key, String data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptionKey = await _getOrCreateEncryptionKey(key);
      final encryptedData = encryptData(data, encryptionKey);
      await prefs.setString(key, encryptedData);
    } catch (e) {
      throw SecurityException('Failed to store secure data: ${e.toString()}');
    }
  }

  // Retrieve and decrypt data from secure storage
  Future<String?> getSecureData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString(key);
      if (encryptedData == null) return null;

      final encryptionKey = await _getOrCreateEncryptionKey(key);
      return decryptData(encryptedData, encryptionKey);
    } catch (e) {
      throw SecurityException(
        'Failed to retrieve secure data: ${e.toString()}',
      );
    }
  }

  // Remove secure data
  Future<void> removeSecureData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
    await prefs.remove(_keyPrefix + key);
  }

  // Sanitize input to prevent injection attacks
  String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'[<>"]'), '')
        .replaceAll("'", '')
        .replaceAll(RegExp(r'script', caseSensitive: false), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .trim();
  }

  // Validate email format
  bool isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  // Validate password strength
  PasswordStrength validatePasswordStrength(String password) {
    if (password.length < 8) {
      return PasswordStrength.weak;
    }

    int score = 0;

    // Length bonus
    if (password.length >= 12)
      score += 2;
    else if (password.length >= 10)
      score += 1;

    // Character variety
    if (RegExp(r'[a-z]').hasMatch(password)) score += 1;
    if (RegExp(r'[A-Z]').hasMatch(password)) score += 1;
    if (RegExp(r'[0-9]').hasMatch(password)) score += 1;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score += 2;

    // Avoid common patterns
    if (RegExp(r'(.)\1{2,}').hasMatch(password))
      score -= 1; // Repeated characters
    if (RegExp(r'123|abc|qwe', caseSensitive: false).hasMatch(password))
      score -= 1;

    if (score >= 6) return PasswordStrength.strong;
    if (score >= 4) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }

  // Generate secure password
  String generateSecurePassword({
    int length = 16,
    bool includeUppercase = true,
    bool includeLowercase = true,
    bool includeNumbers = true,
    bool includeSymbols = true,
  }) {
    String chars = '';
    if (includeLowercase) chars += 'abcdefghijklmnopqrstuvwxyz';
    if (includeUppercase) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (includeNumbers) chars += '0123456789';
    if (includeSymbols) chars += '!@#\$%^&*()_+-=[]{}|;:,.<>?';

    if (chars.isEmpty) {
      throw SecurityException('At least one character type must be included');
    }

    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }

  // Rate limiting for security
  final Map<String, List<DateTime>> _rateLimitMap = {};

  bool checkRateLimit(
    String identifier, {
    int maxAttempts = 5,
    Duration timeWindow = const Duration(minutes: 15),
  }) {
    final now = DateTime.now();
    final attempts = _rateLimitMap[identifier] ?? [];

    // Remove old attempts outside the time window
    attempts.removeWhere((attempt) => now.difference(attempt) > timeWindow);

    if (attempts.length >= maxAttempts) {
      return false; // Rate limit exceeded
    }

    attempts.add(now);
    _rateLimitMap[identifier] = attempts;
    return true;
  }

  // Clear rate limit for identifier
  void clearRateLimit(String identifier) {
    _rateLimitMap.remove(identifier);
  }

  // Private helper methods
  Future<String> _getOrCreateEncryptionKey(String dataKey) async {
    final prefs = await SharedPreferences.getInstance();
    final keyName = _keyPrefix + dataKey;

    String? key = prefs.getString(keyName);
    if (key == null) {
      key = generateSecureKey();
      await prefs.setString(keyName, key);
    }

    return key;
  }

  // Audit logging for security events
  void logSecurityEvent(SecurityEvent event) {
    final timestamp = DateTime.now().toIso8601String();
    print('SECURITY EVENT [$timestamp]: ${event.type} - ${event.description}');

    // In production, you would send this to a secure logging service
    // or store in a secure audit log database
  }
}

enum PasswordStrength { weak, medium, strong }

class SecurityEvent {
  final String type;
  final String description;
  final String? userId;
  final String? ipAddress;
  final DateTime timestamp;

  SecurityEvent({
    required this.type,
    required this.description,
    this.userId,
    this.ipAddress,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}
