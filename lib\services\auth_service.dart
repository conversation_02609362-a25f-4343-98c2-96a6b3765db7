// lib/services/auth_service.dart
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user_model.dart';
import '../database/repositories/users_repository.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final UsersRepository _usersRepository = UsersRepository();
  final Uuid _uuid = const Uuid();
  
  User? _currentUser;
  AuthSession? _currentSession;

  User? get currentUser => _currentUser;
  AuthSession? get currentSession => _currentSession;
  bool get isAuthenticated => _currentUser != null && _currentSession?.isValid == true;

  // Initialize auth service and check for existing session
  Future<bool> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token != null) {
        final session = await _usersRepository.getSessionByToken(token);
        if (session?.isValid == true) {
          final user = await _usersRepository.getById(session!.userId);
          if (user != null) {
            _currentUser = user;
            _currentSession = session;
            await _updateLastLogin(user.id!);
            return true;
          }
        }
      }
      
      await _clearStoredAuth();
      return false;
    } catch (e) {
      await _clearStoredAuth();
      return false;
    }
  }

  // Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String name,
    String? company,
    UserRole role = UserRole.user,
  }) async {
    try {
      // Check if user already exists
      final existingUser = await _usersRepository.getByEmail(email);
      if (existingUser != null) {
        return AuthResult.failure('User with this email already exists');
      }

      // Create new user
      final hashedPassword = _hashPassword(password);
      final user = User(
        email: email,
        name: name,
        company: company,
        role: role,
        status: UserStatus.active,
      );

      final userId = await _usersRepository.create(user, hashedPassword);
      final createdUser = await _usersRepository.getById(userId);
      
      if (createdUser != null) {
        // Create session
        final session = await _createSession(createdUser);
        await _storeAuth(session.token);
        
        _currentUser = createdUser;
        _currentSession = session;
        
        return AuthResult.success(createdUser);
      }
      
      return AuthResult.failure('Failed to create user');
    } catch (e) {
      return AuthResult.failure('Registration failed: ${e.toString()}');
    }
  }

  // Login user
  Future<AuthResult> login({
    required String email,
    required String password,
  }) async {
    try {
      final user = await _usersRepository.getByEmail(email);
      if (user == null) {
        return AuthResult.failure('Invalid email or password');
      }

      if (user.status != UserStatus.active) {
        return AuthResult.failure('Account is not active');
      }

      final storedHash = await _usersRepository.getPasswordHash(user.id!);
      if (storedHash == null || !_verifyPassword(password, storedHash)) {
        return AuthResult.failure('Invalid email or password');
      }

      // Create session
      final session = await _createSession(user);
      await _storeAuth(session.token);
      await _updateLastLogin(user.id!);
      
      _currentUser = user;
      _currentSession = session;
      
      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Login failed: ${e.toString()}');
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      if (_currentSession != null) {
        await _usersRepository.invalidateSession(_currentSession!.id!);
      }
    } catch (e) {
      // Continue with logout even if session invalidation fails
    }
    
    await _clearStoredAuth();
    _currentUser = null;
    _currentSession = null;
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentUser == null) return false;

    try {
      final storedHash = await _usersRepository.getPasswordHash(_currentUser!.id!);
      if (storedHash == null || !_verifyPassword(currentPassword, storedHash)) {
        return false;
      }

      final newHash = _hashPassword(newPassword);
      await _usersRepository.updatePassword(_currentUser!.id!, newHash);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Reset password (would typically involve email verification)
  Future<bool> resetPassword(String email) async {
    try {
      final user = await _usersRepository.getByEmail(email);
      if (user == null) return false;

      // Generate temporary password
      final tempPassword = _generateTempPassword();
      final hashedPassword = _hashPassword(tempPassword);
      
      await _usersRepository.updatePassword(user.id!, hashedPassword);
      
      // In a real app, you would send this via email
      print('Temporary password for $email: $tempPassword');
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Update user profile
  Future<bool> updateProfile(User updatedUser) async {
    if (_currentUser == null) return false;

    try {
      await _usersRepository.update(updatedUser);
      _currentUser = updatedUser;
      return true;
    } catch (e) {
      return false;
    }
  }

  // Check if user has permission
  bool hasPermission(String permission) {
    return _currentUser?.hasPermission(permission) ?? false;
  }

  // Private helper methods
  Future<AuthSession> _createSession(User user) async {
    final token = _generateSecureToken();
    final session = AuthSession(
      userId: user.id!,
      token: token,
      expiresAt: DateTime.now().add(const Duration(days: 30)),
    );

    final sessionId = await _usersRepository.createSession(session);
    return session.copyWith(id: sessionId);
  }

  Future<void> _storeAuth(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  Future<void> _clearStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  Future<void> _updateLastLogin(String userId) async {
    await _usersRepository.updateLastLogin(userId);
  }

  String _hashPassword(String password) {
    final salt = _generateSalt();
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return '$salt:${digest.toString()}';
  }

  bool _verifyPassword(String password, String hash) {
    final parts = hash.split(':');
    if (parts.length != 2) return false;
    
    final salt = parts[0];
    final storedHash = parts[1];
    
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    
    return digest.toString() == storedHash;
  }

  String _generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  String _generateSecureToken() {
    return _uuid.v4() + _uuid.v4().replaceAll('-', '');
  }

  String _generateTempPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random.secure();
    return String.fromCharCodes(Iterable.generate(
      12, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }
}

class AuthResult {
  final bool success;
  final String? error;
  final User? user;

  AuthResult._(this.success, this.error, this.user);

  factory AuthResult.success(User user) => AuthResult._(true, null, user);
  factory AuthResult.failure(String error) => AuthResult._(false, error, null);
}
