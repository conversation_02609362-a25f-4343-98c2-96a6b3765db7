// lib/providers/analytics_provider.dart
import 'package:flutter/foundation.dart';
import '../services/analytics_service.dart';
import '../models/analytics_data.dart';

class AnalyticsProvider with ChangeNotifier {
  final AnalyticsService _analyticsService = AnalyticsService();
  
  bool _isLoading = false;
  String? _error;
  
  // Overview metrics
  int _totalCampaigns = 0;
  int _totalEmailsSent = 0;
  double _averageOpenRate = 0.0;
  double _averageClickRate = 0.0;
  
  // Trends (percentage change)
  double _campaignsTrend = 0.0;
  double _emailsTrend = 0.0;
  double _openRateTrend = 0.0;
  double _clickRateTrend = 0.0;
  
  // Detailed analytics
  List<CampaignAnalytics> _campaignAnalytics = [];
  List<ContactAnalytics> _contactAnalytics = [];
  Map<String, dynamic> _performanceData = {};
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  int get totalCampaigns => _totalCampaigns;
  int get totalEmailsSent => _totalEmailsSent;
  double get averageOpenRate => _averageOpenRate;
  double get averageClickRate => _averageClickRate;
  
  double get campaignsTrend => _campaignsTrend;
  double get emailsTrend => _emailsTrend;
  double get openRateTrend => _openRateTrend;
  double get clickRateTrend => _clickRateTrend;
  
  List<CampaignAnalytics> get campaignAnalytics => _campaignAnalytics;
  List<ContactAnalytics> get contactAnalytics => _contactAnalytics;
  Map<String, dynamic> get performanceData => _performanceData;
  
  Future<void> loadAnalytics(String period) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Load overview metrics
      final overview = await _analyticsService.getOverviewMetrics(period);
      _totalCampaigns = overview['totalCampaigns'] ?? 0;
      _totalEmailsSent = overview['totalEmailsSent'] ?? 0;
      _averageOpenRate = overview['averageOpenRate'] ?? 0.0;
      _averageClickRate = overview['averageClickRate'] ?? 0.0;
      
      // Load trends
      final trends = await _analyticsService.getTrends(period);
      _campaignsTrend = trends['campaignsTrend'] ?? 0.0;
      _emailsTrend = trends['emailsTrend'] ?? 0.0;
      _openRateTrend = trends['openRateTrend'] ?? 0.0;
      _clickRateTrend = trends['clickRateTrend'] ?? 0.0;
      
      // Load detailed analytics
      _campaignAnalytics = await _analyticsService.getCampaignAnalytics(period);
      _contactAnalytics = await _analyticsService.getContactAnalytics(period);
      _performanceData = await _analyticsService.getPerformanceData(period);
      
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<void> exportReport(String format, String period) async {
    try {
      await _analyticsService.exportReport(format, period);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }
  
  Future<List<Map<String, dynamic>>> getCampaignPerformance(String period) async {
    try {
      return await _analyticsService.getCampaignPerformanceData(period);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }
  
  Future<List<Map<String, dynamic>>> getContactEngagement(String period) async {
    try {
      return await _analyticsService.getContactEngagementData(period);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }
  
  Future<Map<String, dynamic>> getDeliveryStats(String period) async {
    try {
      return await _analyticsService.getDeliveryStatistics(period);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return {};
    }
  }
  
  void clearError() {
    _error = null;
    notifyListeners();
  }
}

// Analytics data models
class CampaignAnalytics {
  final String id;
  final String name;
  final DateTime sentDate;
  final int totalRecipients;
  final int delivered;
  final int opened;
  final int clicked;
  final int bounced;
  final int unsubscribed;
  final double openRate;
  final double clickRate;
  final double bounceRate;
  
  CampaignAnalytics({
    required this.id,
    required this.name,
    required this.sentDate,
    required this.totalRecipients,
    required this.delivered,
    required this.opened,
    required this.clicked,
    required this.bounced,
    required this.unsubscribed,
    required this.openRate,
    required this.clickRate,
    required this.bounceRate,
  });
  
  factory CampaignAnalytics.fromMap(Map<String, dynamic> map) {
    return CampaignAnalytics(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      sentDate: DateTime.parse(map['sentDate'] ?? DateTime.now().toIso8601String()),
      totalRecipients: map['totalRecipients'] ?? 0,
      delivered: map['delivered'] ?? 0,
      opened: map['opened'] ?? 0,
      clicked: map['clicked'] ?? 0,
      bounced: map['bounced'] ?? 0,
      unsubscribed: map['unsubscribed'] ?? 0,
      openRate: (map['openRate'] ?? 0.0).toDouble(),
      clickRate: (map['clickRate'] ?? 0.0).toDouble(),
      bounceRate: (map['bounceRate'] ?? 0.0).toDouble(),
    );
  }
}

class ContactAnalytics {
  final String id;
  final String email;
  final String name;
  final int emailsReceived;
  final int emailsOpened;
  final int emailsClicked;
  final DateTime lastActivity;
  final double engagementScore;
  
  ContactAnalytics({
    required this.id,
    required this.email,
    required this.name,
    required this.emailsReceived,
    required this.emailsOpened,
    required this.emailsClicked,
    required this.lastActivity,
    required this.engagementScore,
  });
  
  factory ContactAnalytics.fromMap(Map<String, dynamic> map) {
    return ContactAnalytics(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      name: map['name'] ?? '',
      emailsReceived: map['emailsReceived'] ?? 0,
      emailsOpened: map['emailsOpened'] ?? 0,
      emailsClicked: map['emailsClicked'] ?? 0,
      lastActivity: DateTime.parse(map['lastActivity'] ?? DateTime.now().toIso8601String()),
      engagementScore: (map['engagementScore'] ?? 0.0).toDouble(),
    );
  }
}
