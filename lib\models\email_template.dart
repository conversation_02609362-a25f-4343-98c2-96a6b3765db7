// lib/models/email_template.dart
import 'package:uuid/uuid.dart';

enum TemplateType {
  newsletter,
  promotional,
  transactional,
  welcome,
  followUp,
  announcement,
  custom,
}

enum TemplateStatus {
  draft,
  active,
  archived,
}

class EmailTemplate {
  final String id;
  final String name;
  final String subject;
  final String htmlContent;
  final String? plainTextContent;
  final TemplateType type;
  final TemplateStatus status;
  final String? description;
  final List<String> tags;
  final Map<String, dynamic> variables;
  final Map<String, dynamic> settings;
  final String? previewText;
  final String? thumbnailUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;
  final int usageCount;
  final DateTime? lastUsedAt;

  EmailTemplate({
    String? id,
    required this.name,
    required this.subject,
    required this.htmlContent,
    this.plainTextContent,
    this.type = TemplateType.custom,
    this.status = TemplateStatus.draft,
    this.description,
    List<String>? tags,
    Map<String, dynamic>? variables,
    Map<String, dynamic>? settings,
    this.previewText,
    this.thumbnailUrl,
    DateTime? createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.usageCount = 0,
    this.lastUsedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       tags = tags ?? [],
       variables = variables ?? {},
       settings = settings ?? {};

  EmailTemplate copyWith({
    String? id,
    String? name,
    String? subject,
    String? htmlContent,
    String? plainTextContent,
    TemplateType? type,
    TemplateStatus? status,
    String? description,
    List<String>? tags,
    Map<String, dynamic>? variables,
    Map<String, dynamic>? settings,
    String? previewText,
    String? thumbnailUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    int? usageCount,
    DateTime? lastUsedAt,
  }) {
    return EmailTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      htmlContent: htmlContent ?? this.htmlContent,
      plainTextContent: plainTextContent ?? this.plainTextContent,
      type: type ?? this.type,
      status: status ?? this.status,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      variables: variables ?? this.variables,
      settings: settings ?? this.settings,
      previewText: previewText ?? this.previewText,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      usageCount: usageCount ?? this.usageCount,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'html_content': htmlContent,
      'plain_text_content': plainTextContent,
      'type': type.toString(),
      'status': status.toString(),
      'description': description,
      'tags': tags.join(','),
      'variables': variables.isNotEmpty ? 
          variables.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
      'settings': settings.isNotEmpty ? 
          settings.entries.map((e) => '${e.key}:${e.value}').join('|') : null,
      'preview_text': previewText,
      'thumbnail_url': thumbnailUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'created_by': createdBy,
      'updated_by': updatedBy,
      'usage_count': usageCount,
      'last_used_at': lastUsedAt?.toIso8601String(),
    };
  }

  factory EmailTemplate.fromMap(Map<String, dynamic> map) {
    final variablesStr = map['variables'] as String?;
    final variables = <String, dynamic>{};
    
    if (variablesStr != null && variablesStr.isNotEmpty) {
      for (final pair in variablesStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          variables[parts[0]] = parts[1];
        }
      }
    }

    final settingsStr = map['settings'] as String?;
    final settings = <String, dynamic>{};
    
    if (settingsStr != null && settingsStr.isNotEmpty) {
      for (final pair in settingsStr.split('|')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          settings[parts[0]] = parts[1];
        }
      }
    }

    return EmailTemplate(
      id: map['id'] as String,
      name: map['name'] as String,
      subject: map['subject'] as String,
      htmlContent: map['html_content'] as String,
      plainTextContent: map['plain_text_content'] as String?,
      type: TemplateType.values.firstWhere(
        (t) => t.toString() == map['type'],
        orElse: () => TemplateType.custom,
      ),
      status: TemplateStatus.values.firstWhere(
        (s) => s.toString() == map['status'],
        orElse: () => TemplateStatus.draft,
      ),
      description: map['description'] as String?,
      tags: (map['tags'] as String?)?.split(',').where((tag) => tag.isNotEmpty).toList() ?? [],
      variables: variables,
      settings: settings,
      previewText: map['preview_text'] as String?,
      thumbnailUrl: map['thumbnail_url'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at'] as String) : null,
      createdBy: map['created_by'] as String?,
      updatedBy: map['updated_by'] as String?,
      usageCount: map['usage_count'] as int? ?? 0,
      lastUsedAt: map['last_used_at'] != null ? DateTime.parse(map['last_used_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory EmailTemplate.fromJson(Map<String, dynamic> json) => EmailTemplate.fromMap(json);

  @override
  String toString() {
    return 'EmailTemplate(id: $id, name: $name, type: $type, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmailTemplate && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  bool get isDraft => status == TemplateStatus.draft;
  bool get isActive => status == TemplateStatus.active;
  bool get isArchived => status == TemplateStatus.archived;

  String get typeDisplayName {
    switch (type) {
      case TemplateType.newsletter:
        return 'Newsletter';
      case TemplateType.promotional:
        return 'Promotional';
      case TemplateType.transactional:
        return 'Transactional';
      case TemplateType.welcome:
        return 'Welcome';
      case TemplateType.followUp:
        return 'Follow-up';
      case TemplateType.announcement:
        return 'Announcement';
      case TemplateType.custom:
        return 'Custom';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TemplateStatus.draft:
        return 'Draft';
      case TemplateStatus.active:
        return 'Active';
      case TemplateStatus.archived:
        return 'Archived';
    }
  }

  bool get hasBeenUsed => usageCount > 0;

  Duration? get timeSinceLastUse {
    if (lastUsedAt != null) {
      return DateTime.now().difference(lastUsedAt!);
    }
    return null;
  }

  bool get isRecentlyUsed {
    final timeSince = timeSinceLastUse;
    return timeSince != null && timeSince.inDays <= 30;
  }

  EmailTemplate activate() => copyWith(status: TemplateStatus.active, updatedAt: DateTime.now());
  EmailTemplate archive() => copyWith(status: TemplateStatus.archived, updatedAt: DateTime.now());
  EmailTemplate makeDraft() => copyWith(status: TemplateStatus.draft, updatedAt: DateTime.now());

  EmailTemplate incrementUsage() {
    return copyWith(
      usageCount: usageCount + 1,
      lastUsedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  EmailTemplate addTag(String tag) {
    if (!tags.contains(tag)) {
      return copyWith(
        tags: [...tags, tag],
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  EmailTemplate removeTag(String tag) {
    return copyWith(
      tags: tags.where((t) => t != tag).toList(),
      updatedAt: DateTime.now(),
    );
  }

  EmailTemplate addVariable(String key, dynamic defaultValue) {
    final newVariables = Map<String, dynamic>.from(variables);
    newVariables[key] = defaultValue;
    return copyWith(
      variables: newVariables,
      updatedAt: DateTime.now(),
    );
  }

  EmailTemplate removeVariable(String key) {
    final newVariables = Map<String, dynamic>.from(variables);
    newVariables.remove(key);
    return copyWith(
      variables: newVariables,
      updatedAt: DateTime.now(),
    );
  }

  EmailTemplate updateSetting(String key, dynamic value) {
    final newSettings = Map<String, dynamic>.from(settings);
    newSettings[key] = value;
    return copyWith(
      settings: newSettings,
      updatedAt: DateTime.now(),
    );
  }

  EmailTemplate removeSetting(String key) {
    final newSettings = Map<String, dynamic>.from(settings);
    newSettings.remove(key);
    return copyWith(
      settings: newSettings,
      updatedAt: DateTime.now(),
    );
  }

  String renderSubject(Map<String, dynamic> data) {
    String rendered = subject;
    for (final entry in data.entries) {
      rendered = rendered.replaceAll('{{${entry.key}}}', entry.value.toString());
    }
    return rendered;
  }

  String renderHtmlContent(Map<String, dynamic> data) {
    String rendered = htmlContent;
    for (final entry in data.entries) {
      rendered = rendered.replaceAll('{{${entry.key}}}', entry.value.toString());
    }
    return rendered;
  }

  String? renderPlainTextContent(Map<String, dynamic> data) {
    if (plainTextContent == null) return null;
    String rendered = plainTextContent!;
    for (final entry in data.entries) {
      rendered = rendered.replaceAll('{{${entry.key}}}', entry.value.toString());
    }
    return rendered;
  }

  List<String> extractVariables() {
    final regex = RegExp(r'\{\{(\w+)\}\}');
    final variables = <String>{};
    
    // Extract from subject
    variables.addAll(regex.allMatches(subject).map((m) => m.group(1)!));
    
    // Extract from HTML content
    variables.addAll(regex.allMatches(htmlContent).map((m) => m.group(1)!));
    
    // Extract from plain text content
    if (plainTextContent != null) {
      variables.addAll(regex.allMatches(plainTextContent!).map((m) => m.group(1)!));
    }
    
    return variables.toList();
  }

  bool hasVariable(String variableName) {
    return extractVariables().contains(variableName);
  }

  Map<String, dynamic> getTemplateInfo() {
    return {
      'id': id,
      'name': name,
      'type': typeDisplayName,
      'status': statusDisplayName,
      'usageCount': usageCount,
      'hasBeenUsed': hasBeenUsed,
      'isRecentlyUsed': isRecentlyUsed,
      'variableCount': variables.length,
      'tagCount': tags.length,
      'createdAt': createdAt.toIso8601String(),
      'lastUsedAt': lastUsedAt?.toIso8601String(),
    };
  }
}
