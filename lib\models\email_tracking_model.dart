// lib/models/email_tracking_model.dart

enum EmailStatus {
  queued,
  sending,
  sent,
  delivered,
  opened,
  clicked,
  bounced,
  failed,
  unsubscribed,
  complained,
}

enum BounceType {
  hard,
  soft,
  unknown,
}

class EmailTracking {
  final String? id;
  final String campaignId;
  final String recipientId;
  final String recipientEmail;
  final EmailStatus status;
  final DateTime createdAt;
  final DateTime? sentAt;
  final DateTime? deliveredAt;
  final DateTime? openedAt;
  final DateTime? clickedAt;
  final DateTime? bouncedAt;
  final DateTime? failedAt;
  final String? errorMessage;
  final BounceType? bounceType;
  final String? bounceReason;
  final Map<String, dynamic> metadata;
  final List<EmailEvent> events;

  EmailTracking({
    this.id,
    required this.campaignId,
    required this.recipientId,
    required this.recipientEmail,
    this.status = EmailStatus.queued,
    DateTime? createdAt,
    this.sentAt,
    this.deliveredAt,
    this.openedAt,
    this.clickedAt,
    this.bouncedAt,
    this.failedAt,
    this.errorMessage,
    this.bounceType,
    this.bounceReason,
    this.metadata = const {},
    this.events = const [],
  }) : createdAt = createdAt ?? DateTime.now();

  factory EmailTracking.fromJson(Map<String, dynamic> json) {
    return EmailTracking(
      id: json['id'],
      campaignId: json['campaignId'],
      recipientId: json['recipientId'],
      recipientEmail: json['recipientEmail'],
      status: EmailStatus.values.firstWhere(
        (e) => e.toString() == 'EmailStatus.${json['status']}',
        orElse: () => EmailStatus.queued,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      sentAt: json['sentAt'] != null ? DateTime.parse(json['sentAt']) : null,
      deliveredAt: json['deliveredAt'] != null ? DateTime.parse(json['deliveredAt']) : null,
      openedAt: json['openedAt'] != null ? DateTime.parse(json['openedAt']) : null,
      clickedAt: json['clickedAt'] != null ? DateTime.parse(json['clickedAt']) : null,
      bouncedAt: json['bouncedAt'] != null ? DateTime.parse(json['bouncedAt']) : null,
      failedAt: json['failedAt'] != null ? DateTime.parse(json['failedAt']) : null,
      errorMessage: json['errorMessage'],
      bounceType: json['bounceType'] != null 
        ? BounceType.values.firstWhere(
            (e) => e.toString() == 'BounceType.${json['bounceType']}',
            orElse: () => BounceType.unknown,
          )
        : null,
      bounceReason: json['bounceReason'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      events: (json['events'] as List<dynamic>?)
          ?.map((e) => EmailEvent.fromJson(e))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'campaignId': campaignId,
      'recipientId': recipientId,
      'recipientEmail': recipientEmail,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'sentAt': sentAt?.toIso8601String(),
      'deliveredAt': deliveredAt?.toIso8601String(),
      'openedAt': openedAt?.toIso8601String(),
      'clickedAt': clickedAt?.toIso8601String(),
      'bouncedAt': bouncedAt?.toIso8601String(),
      'failedAt': failedAt?.toIso8601String(),
      'errorMessage': errorMessage,
      'bounceType': bounceType?.toString().split('.').last,
      'bounceReason': bounceReason,
      'metadata': metadata,
      'events': events.map((e) => e.toJson()).toList(),
    };
  }

  EmailTracking copyWith({
    String? id,
    String? campaignId,
    String? recipientId,
    String? recipientEmail,
    EmailStatus? status,
    DateTime? createdAt,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? openedAt,
    DateTime? clickedAt,
    DateTime? bouncedAt,
    DateTime? failedAt,
    String? errorMessage,
    BounceType? bounceType,
    String? bounceReason,
    Map<String, dynamic>? metadata,
    List<EmailEvent>? events,
  }) {
    return EmailTracking(
      id: id ?? this.id,
      campaignId: campaignId ?? this.campaignId,
      recipientId: recipientId ?? this.recipientId,
      recipientEmail: recipientEmail ?? this.recipientEmail,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      sentAt: sentAt ?? this.sentAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      openedAt: openedAt ?? this.openedAt,
      clickedAt: clickedAt ?? this.clickedAt,
      bouncedAt: bouncedAt ?? this.bouncedAt,
      failedAt: failedAt ?? this.failedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      bounceType: bounceType ?? this.bounceType,
      bounceReason: bounceReason ?? this.bounceReason,
      metadata: metadata ?? this.metadata,
      events: events ?? this.events,
    );
  }
}

enum EmailEventType {
  sent,
  delivered,
  opened,
  clicked,
  bounced,
  failed,
  unsubscribed,
  complained,
}

class EmailEvent {
  final String? id;
  final EmailEventType type;
  final DateTime timestamp;
  final String? userAgent;
  final String? ipAddress;
  final String? clickedUrl;
  final Map<String, dynamic> data;

  EmailEvent({
    this.id,
    required this.type,
    DateTime? timestamp,
    this.userAgent,
    this.ipAddress,
    this.clickedUrl,
    this.data = const {},
  }) : timestamp = timestamp ?? DateTime.now();

  factory EmailEvent.fromJson(Map<String, dynamic> json) {
    return EmailEvent(
      id: json['id'],
      type: EmailEventType.values.firstWhere(
        (e) => e.toString() == 'EmailEventType.${json['type']}',
        orElse: () => EmailEventType.sent,
      ),
      timestamp: DateTime.parse(json['timestamp']),
      userAgent: json['userAgent'],
      ipAddress: json['ipAddress'],
      clickedUrl: json['clickedUrl'],
      data: Map<String, dynamic>.from(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'userAgent': userAgent,
      'ipAddress': ipAddress,
      'clickedUrl': clickedUrl,
      'data': data,
    };
  }
}
