// lib/database/repositories/users_repository.dart
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../database_helper.dart';
import '../../models/user_model.dart';

class UsersRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  Future<String> create(User user, String passwordHash) async {
    final db = await _dbHelper.database;
    final id = user.id ?? _uuid.v4();
    
    final userWithId = user.copyWith(
      id: id,
      updatedAt: DateTime.now(),
    );

    await db.transaction((txn) async {
      // Insert user
      await txn.insert(
        'users',
        {
          'id': userWithId.id,
          'email': userWithId.email,
          'name': userWithId.name,
          'company': userWithId.company,
          'role': userWithId.role.toString().split('.').last,
          'status': userWithId.status.toString().split('.').last,
          'created_at': userWithId.createdAt.toIso8601String(),
          'updated_at': userWithId.updatedAt?.toIso8601String(),
          'last_login_at': userWithId.lastLoginAt?.toIso8601String(),
          'preferences': jsonEncode(userWithId.preferences),
          'permissions': jsonEncode(userWithId.permissions),
          'avatar_url': userWithId.avatarUrl,
          'phone': userWithId.phone,
          'timezone': userWithId.timezone,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Insert password hash in separate table for security
      await txn.insert(
        'user_passwords',
        {
          'user_id': userWithId.id,
          'password_hash': passwordHash,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    });

    return id;
  }

  Future<User?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToUser(maps.first);
  }

  Future<User?> getByEmail(String email) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToUser(maps.first);
  }

  Future<List<User>> getAll({
    int? limit,
    int? offset,
    String? searchQuery,
    UserRole? role,
    UserStatus? status,
  }) async {
    final db = await _dbHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += 'name LIKE ? OR email LIKE ? OR company LIKE ?';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%', '%$searchQuery%']);
    }

    if (role != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'role = ?';
      whereArgs.add(role.toString().split('.').last);
    }

    if (status != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'status = ?';
      whereArgs.add(status.toString().split('.').last);
    }

    final maps = await db.query(
      'users',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map(_mapToUser).toList();
  }

  Future<void> update(User user) async {
    final db = await _dbHelper.database;
    
    final updatedUser = user.copyWith(updatedAt: DateTime.now());

    await db.update(
      'users',
      {
        'email': updatedUser.email,
        'name': updatedUser.name,
        'company': updatedUser.company,
        'role': updatedUser.role.toString().split('.').last,
        'status': updatedUser.status.toString().split('.').last,
        'updated_at': updatedUser.updatedAt?.toIso8601String(),
        'preferences': jsonEncode(updatedUser.preferences),
        'permissions': jsonEncode(updatedUser.permissions),
        'avatar_url': updatedUser.avatarUrl,
        'phone': updatedUser.phone,
        'timezone': updatedUser.timezone,
      },
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<void> updateLastLogin(String userId) async {
    final db = await _dbHelper.database;
    await db.update(
      'users',
      {
        'last_login_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  Future<void> updateStatus(String userId, UserStatus status) async {
    final db = await _dbHelper.database;
    await db.update(
      'users',
      {
        'status': status.toString().split('.').last,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.transaction((txn) async {
      await txn.delete('user_passwords', where: 'user_id = ?', whereArgs: [id]);
      await txn.delete('auth_sessions', where: 'user_id = ?', whereArgs: [id]);
      await txn.delete('users', where: 'id = ?', whereArgs: [id]);
    });
  }

  // Password management
  Future<String?> getPasswordHash(String userId) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'user_passwords',
      columns: ['password_hash'],
      where: 'user_id = ?',
      whereArgs: [userId],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return maps.first['password_hash'] as String;
  }

  Future<void> updatePassword(String userId, String passwordHash) async {
    final db = await _dbHelper.database;
    await db.update(
      'user_passwords',
      {
        'password_hash': passwordHash,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }

  // Session management
  Future<String> createSession(AuthSession session) async {
    final db = await _dbHelper.database;
    final id = session.id ?? _uuid.v4();
    
    await db.insert(
      'auth_sessions',
      {
        'id': id,
        'user_id': session.userId,
        'token': session.token,
        'created_at': session.createdAt.toIso8601String(),
        'expires_at': session.expiresAt.toIso8601String(),
        'device_info': session.deviceInfo,
        'ip_address': session.ipAddress,
        'is_active': session.isActive ? 1 : 0,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    return id;
  }

  Future<AuthSession?> getSessionByToken(String token) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'auth_sessions',
      where: 'token = ? AND is_active = 1',
      whereArgs: [token],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _mapToAuthSession(maps.first);
  }

  Future<List<AuthSession>> getUserSessions(String userId) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'auth_sessions',
      where: 'user_id = ? AND is_active = 1',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return maps.map(_mapToAuthSession).toList();
  }

  Future<void> invalidateSession(String sessionId) async {
    final db = await _dbHelper.database;
    await db.update(
      'auth_sessions',
      {'is_active': 0},
      where: 'id = ?',
      whereArgs: [sessionId],
    );
  }

  Future<void> invalidateAllUserSessions(String userId) async {
    final db = await _dbHelper.database;
    await db.update(
      'auth_sessions',
      {'is_active': 0},
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }

  Future<void> cleanupExpiredSessions() async {
    final db = await _dbHelper.database;
    await db.delete(
      'auth_sessions',
      where: 'expires_at < ?',
      whereArgs: [DateTime.now().toIso8601String()],
    );
  }

  User _mapToUser(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      email: map['email'],
      name: map['name'],
      company: map['company'],
      role: UserRole.values.firstWhere(
        (e) => e.toString() == 'UserRole.${map['role']}',
        orElse: () => UserRole.user,
      ),
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == 'UserStatus.${map['status']}',
        orElse: () => UserStatus.active,
      ),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      lastLoginAt: map['last_login_at'] != null ? DateTime.parse(map['last_login_at']) : null,
      preferences: _parseJson(map['preferences']),
      permissions: _parseJsonList(map['permissions']),
      avatarUrl: map['avatar_url'],
      phone: map['phone'],
      timezone: map['timezone'],
    );
  }

  AuthSession _mapToAuthSession(Map<String, dynamic> map) {
    return AuthSession(
      id: map['id'],
      userId: map['user_id'],
      token: map['token'],
      createdAt: DateTime.parse(map['created_at']),
      expiresAt: DateTime.parse(map['expires_at']),
      deviceInfo: map['device_info'],
      ipAddress: map['ip_address'],
      isActive: map['is_active'] == 1,
    );
  }

  Map<String, dynamic> _parseJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return {};
    try {
      return Map<String, dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      return {};
    }
  }

  List<String> _parseJsonList(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return [];
    try {
      return List<String>.from(jsonDecode(jsonString));
    } catch (e) {
      return [];
    }
  }
}
