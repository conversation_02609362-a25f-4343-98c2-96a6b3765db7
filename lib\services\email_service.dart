// lib/services/email_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import '../models/email_service_config_model.dart';
import '../models/recipient_model.dart';
import '../models/email_tracking_model.dart';
import '../database/repositories/email_service_configs_repository.dart';
import '../database/repositories/email_tracking_repository.dart';

class EmailService {
  static final EmailService _instance = EmailService._internal();
  factory EmailService() => _instance;
  EmailService._internal();

  final EmailServiceConfigsRepository _configRepository = EmailServiceConfigsRepository();
  final EmailTrackingRepository _trackingRepository = EmailTrackingRepository();

  // Send email using configured service
  Future<EmailSendResult> sendEmail({
    required String to,
    required String toName,
    required String subject,
    required String htmlContent,
    String? plainTextContent,
    List<String>? cc,
    List<String>? bcc,
    Map<String, String>? headers,
    String? campaignId,
    String? templateId,
  }) async {
    try {
      // Get active email service configuration
      final config = await _getActiveEmailConfig();
      if (config == null) {
        throw EmailServiceException('No active email service configuration found');
      }

      // Create tracking record
      final trackingId = await _createTrackingRecord(
        recipientEmail: to,
        subject: subject,
        campaignId: campaignId,
        templateId: templateId,
        serviceProvider: config.provider,
      );

      EmailSendResult result;

      // Send email based on provider
      switch (config.provider) {
        case EmailProvider.sendgrid:
          result = await _sendWithSendGrid(config, to, toName, subject, htmlContent, plainTextContent, cc, bcc, headers, trackingId);
          break;
        case EmailProvider.mailgun:
          result = await _sendWithMailgun(config, to, toName, subject, htmlContent, plainTextContent, cc, bcc, headers, trackingId);
          break;
        case EmailProvider.awsSes:
          result = await _sendWithAwsSes(config, to, toName, subject, htmlContent, plainTextContent, cc, bcc, headers, trackingId);
          break;
        case EmailProvider.smtp:
          result = await _sendWithSmtp(config, to, toName, subject, htmlContent, plainTextContent, cc, bcc, headers, trackingId);
          break;
        default:
          throw EmailServiceException('Unsupported email provider: ${config.provider}');
      }

      // Update tracking record with result
      await _updateTrackingRecord(trackingId, result);

      return result;
    } catch (e) {
      throw EmailServiceException('Failed to send email: ${e.toString()}');
    }
  }

  // Send bulk emails
  Future<BulkEmailResult> sendBulkEmails({
    required List<Recipient> recipients,
    required String subject,
    required String htmlContent,
    String? plainTextContent,
    Map<String, String>? headers,
    String? campaignId,
    String? templateId,
    int batchSize = 100,
    Duration batchDelay = const Duration(seconds: 1),
  }) async {
    final results = <EmailSendResult>[];
    final errors = <String>[];
    int successCount = 0;
    int failureCount = 0;

    try {
      // Process recipients in batches
      for (int i = 0; i < recipients.length; i += batchSize) {
        final batch = recipients.skip(i).take(batchSize).toList();
        
        for (final recipient in batch) {
          try {
            final result = await sendEmail(
              to: recipient.email,
              toName: recipient.name,
              subject: subject,
              htmlContent: htmlContent,
              plainTextContent: plainTextContent,
              headers: headers,
              campaignId: campaignId,
              templateId: templateId,
            );
            
            results.add(result);
            if (result.success) {
              successCount++;
            } else {
              failureCount++;
              errors.add('${recipient.email}: ${result.error}');
            }
          } catch (e) {
            failureCount++;
            errors.add('${recipient.email}: ${e.toString()}');
          }
        }

        // Delay between batches to avoid rate limiting
        if (i + batchSize < recipients.length) {
          await Future.delayed(batchDelay);
        }
      }

      return BulkEmailResult(
        totalEmails: recipients.length,
        successCount: successCount,
        failureCount: failureCount,
        results: results,
        errors: errors,
      );
    } catch (e) {
      throw EmailServiceException('Failed to send bulk emails: ${e.toString()}');
    }
  }

  // SendGrid implementation
  Future<EmailSendResult> _sendWithSendGrid(
    EmailServiceConfig config,
    String to,
    String toName,
    String subject,
    String htmlContent,
    String? plainTextContent,
    List<String>? cc,
    List<String>? bcc,
    Map<String, String>? headers,
    String trackingId,
  ) async {
    try {
      final url = Uri.parse('https://api.sendgrid.com/v3/mail/send');
      
      final emailData = {
        'personalizations': [
          {
            'to': [{'email': to, 'name': toName}],
            if (cc != null && cc.isNotEmpty) 'cc': cc.map((email) => {'email': email}).toList(),
            if (bcc != null && bcc.isNotEmpty) 'bcc': bcc.map((email) => {'email': email}).toList(),
            'subject': subject,
            'custom_args': {'tracking_id': trackingId},
          }
        ],
        'from': {'email': config.fromEmail, 'name': config.fromName},
        'content': [
          {'type': 'text/html', 'value': htmlContent},
          if (plainTextContent != null) {'type': 'text/plain', 'value': plainTextContent},
        ],
        'tracking_settings': {
          'click_tracking': {'enable': true},
          'open_tracking': {'enable': true},
        },
        if (headers != null) 'headers': headers,
      };

      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer ${config.apiKey}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(emailData),
      );

      if (response.statusCode == 202) {
        return EmailSendResult(
          success: true,
          messageId: response.headers['x-message-id'],
          trackingId: trackingId,
          provider: EmailProvider.sendgrid,
        );
      } else {
        final errorData = jsonDecode(response.body);
        return EmailSendResult(
          success: false,
          error: errorData['errors']?.first['message'] ?? 'Unknown SendGrid error',
          trackingId: trackingId,
          provider: EmailProvider.sendgrid,
        );
      }
    } catch (e) {
      return EmailSendResult(
        success: false,
        error: e.toString(),
        trackingId: trackingId,
        provider: EmailProvider.sendgrid,
      );
    }
  }

  // Mailgun implementation
  Future<EmailSendResult> _sendWithMailgun(
    EmailServiceConfig config,
    String to,
    String toName,
    String subject,
    String htmlContent,
    String? plainTextContent,
    List<String>? cc,
    List<String>? bcc,
    Map<String, String>? headers,
    String trackingId,
  ) async {
    try {
      final domain = config.settings['domain'] as String;
      final url = Uri.parse('https://api.mailgun.net/v3/$domain/messages');
      
      final formData = {
        'from': '${config.fromName} <${config.fromEmail}>',
        'to': '$toName <$to>',
        'subject': subject,
        'html': htmlContent,
        if (plainTextContent != null) 'text': plainTextContent,
        if (cc != null && cc.isNotEmpty) 'cc': cc.join(','),
        if (bcc != null && bcc.isNotEmpty) 'bcc': bcc.join(','),
        'o:tracking': 'yes',
        'o:tracking-clicks': 'yes',
        'o:tracking-opens': 'yes',
        'v:tracking_id': trackingId,
      };

      if (headers != null) {
        for (final entry in headers.entries) {
          formData['h:${entry.key}'] = entry.value;
        }
      }

      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Basic ${base64Encode(utf8.encode('api:${config.apiKey}'))}',
        },
        body: formData,
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return EmailSendResult(
          success: true,
          messageId: responseData['id'],
          trackingId: trackingId,
          provider: EmailProvider.mailgun,
        );
      } else {
        final errorData = jsonDecode(response.body);
        return EmailSendResult(
          success: false,
          error: errorData['message'] ?? 'Unknown Mailgun error',
          trackingId: trackingId,
          provider: EmailProvider.mailgun,
        );
      }
    } catch (e) {
      return EmailSendResult(
        success: false,
        error: e.toString(),
        trackingId: trackingId,
        provider: EmailProvider.mailgun,
      );
    }
  }

  // AWS SES implementation
  Future<EmailSendResult> _sendWithAwsSes(
    EmailServiceConfig config,
    String to,
    String toName,
    String subject,
    String htmlContent,
    String? plainTextContent,
    List<String>? cc,
    List<String>? bcc,
    Map<String, String>? headers,
    String trackingId,
  ) async {
    try {
      // AWS SES implementation would require AWS SDK
      // For now, return a placeholder implementation
      return EmailSendResult(
        success: false,
        error: 'AWS SES implementation requires AWS SDK integration',
        trackingId: trackingId,
        provider: EmailProvider.awsSes,
      );
    } catch (e) {
      return EmailSendResult(
        success: false,
        error: e.toString(),
        trackingId: trackingId,
        provider: EmailProvider.awsSes,
      );
    }
  }

  // SMTP implementation
  Future<EmailSendResult> _sendWithSmtp(
    EmailServiceConfig config,
    String to,
    String toName,
    String subject,
    String htmlContent,
    String? plainTextContent,
    List<String>? cc,
    List<String>? bcc,
    Map<String, String>? headers,
    String trackingId,
  ) async {
    try {
      final smtpServer = SmtpServer(
        config.settings['host'] as String,
        port: int.parse(config.settings['port'] as String? ?? '587'),
        username: config.settings['username'] as String?,
        password: config.apiKey, // Using apiKey as password for SMTP
        ssl: config.settings['ssl'] == 'true',
        allowInsecure: config.settings['allowInsecure'] == 'true',
      );

      final message = Message()
        ..from = Address(config.fromEmail, config.fromName)
        ..recipients.add(Address(to, toName))
        ..subject = subject
        ..html = htmlContent;

      if (plainTextContent != null) {
        message.text = plainTextContent;
      }

      if (cc != null && cc.isNotEmpty) {
        message.ccRecipients.addAll(cc.map((email) => Address(email)));
      }

      if (bcc != null && bcc.isNotEmpty) {
        message.bccRecipients.addAll(bcc.map((email) => Address(email)));
      }

      if (headers != null) {
        for (final entry in headers.entries) {
          message.headers[entry.key] = entry.value;
        }
      }

      // Add tracking header
      message.headers['X-Tracking-ID'] = trackingId;

      final sendReport = await send(message, smtpServer);

      return EmailSendResult(
        success: true,
        messageId: sendReport.toString(),
        trackingId: trackingId,
        provider: EmailProvider.smtp,
      );
    } catch (e) {
      return EmailSendResult(
        success: false,
        error: e.toString(),
        trackingId: trackingId,
        provider: EmailProvider.smtp,
      );
    }
  }

  // Helper methods
  Future<EmailServiceConfig?> _getActiveEmailConfig() async {
    try {
      final configs = await _configRepository.getActive();
      return configs.isNotEmpty ? configs.first : null;
    } catch (e) {
      return null;
    }
  }

  Future<String> _createTrackingRecord({
    required String recipientEmail,
    required String subject,
    String? campaignId,
    String? templateId,
    required EmailProvider serviceProvider,
  }) async {
    final tracking = EmailTracking(
      recipientEmail: recipientEmail,
      subject: subject,
      campaignId: campaignId,
      templateId: templateId,
      status: EmailStatus.sent,
      serviceProvider: serviceProvider.toString(),
      events: [],
    );

    return await _trackingRepository.create(tracking);
  }

  Future<void> _updateTrackingRecord(String trackingId, EmailSendResult result) async {
    try {
      final tracking = await _trackingRepository.getById(trackingId);
      if (tracking != null) {
        final updatedTracking = tracking.copyWith(
          status: result.success ? EmailStatus.delivered : EmailStatus.failed,
          messageId: result.messageId,
          error: result.error,
          deliveredAt: result.success ? DateTime.now() : null,
        );
        await _trackingRepository.update(updatedTracking);
      }
    } catch (e) {
      // Log error but don't throw - tracking failure shouldn't prevent email sending
      print('Failed to update tracking record: $e');
    }
  }
}

class EmailSendResult {
  final bool success;
  final String? messageId;
  final String? error;
  final String trackingId;
  final EmailProvider provider;

  EmailSendResult({
    required this.success,
    this.messageId,
    this.error,
    required this.trackingId,
    required this.provider,
  });
}

class BulkEmailResult {
  final int totalEmails;
  final int successCount;
  final int failureCount;
  final List<EmailSendResult> results;
  final List<String> errors;

  BulkEmailResult({
    required this.totalEmails,
    required this.successCount,
    required this.failureCount,
    required this.results,
    required this.errors,
  });

  double get successRate => totalEmails > 0 ? (successCount / totalEmails) * 100 : 0;
  bool get hasErrors => failureCount > 0;
}

class EmailServiceException implements Exception {
  final String message;
  EmailServiceException(this.message);
  
  @override
  String toString() => 'EmailServiceException: $message';
}
