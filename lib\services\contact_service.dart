// lib/services/contact_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:csv/csv.dart';
import '../models/recipient_model.dart';
import '../models/contact_group_model.dart';
import '../database/repositories/recipients_repository.dart';
import '../database/repositories/contact_groups_repository.dart';

class ContactService {
  static final ContactService _instance = ContactService._internal();
  factory ContactService() => _instance;
  ContactService._internal();

  final RecipientsRepository _recipientsRepository = RecipientsRepository();
  final ContactGroupsRepository _groupsRepository = ContactGroupsRepository();

  // Contact Management
  Future<String> createContact(Recipient contact) async {
    try {
      return await _recipientsRepository.create(contact);
    } catch (e) {
      throw ContactException('Failed to create contact: ${e.toString()}');
    }
  }

  Future<List<String>> createContacts(List<Recipient> contacts) async {
    try {
      return await _recipientsRepository.createBatch(contacts);
    } catch (e) {
      throw ContactException('Failed to create contacts: ${e.toString()}');
    }
  }

  Future<Recipient?> getContact(String id) async {
    try {
      return await _recipientsRepository.getById(id);
    } catch (e) {
      throw ContactException('Failed to get contact: ${e.toString()}');
    }
  }

  Future<Recipient?> getContactByEmail(String email) async {
    try {
      return await _recipientsRepository.getByEmail(email);
    } catch (e) {
      throw ContactException('Failed to get contact by email: ${e.toString()}');
    }
  }

  Future<List<Recipient>> getContacts({
    int? limit,
    int? offset,
    String? searchQuery,
    List<String>? tags,
    List<String>? groups,
    bool? isSubscribed,
  }) async {
    try {
      return await _recipientsRepository.getAll(
        limit: limit,
        offset: offset,
        searchQuery: searchQuery,
        tags: tags,
        groups: groups,
        isSubscribed: isSubscribed,
      );
    } catch (e) {
      throw ContactException('Failed to get contacts: ${e.toString()}');
    }
  }

  Future<int> getContactCount({
    String? searchQuery,
    List<String>? tags,
    List<String>? groups,
    bool? isSubscribed,
  }) async {
    try {
      return await _recipientsRepository.getCount(
        searchQuery: searchQuery,
        tags: tags,
        groups: groups,
        isSubscribed: isSubscribed,
      );
    } catch (e) {
      throw ContactException('Failed to get contact count: ${e.toString()}');
    }
  }

  Future<void> updateContact(Recipient contact) async {
    try {
      await _recipientsRepository.update(contact);
    } catch (e) {
      throw ContactException('Failed to update contact: ${e.toString()}');
    }
  }

  Future<void> deleteContact(String id) async {
    try {
      await _recipientsRepository.delete(id);
    } catch (e) {
      throw ContactException('Failed to delete contact: ${e.toString()}');
    }
  }

  Future<void> deleteContacts(List<String> ids) async {
    try {
      await _recipientsRepository.deleteBatch(ids);
    } catch (e) {
      throw ContactException('Failed to delete contacts: ${e.toString()}');
    }
  }

  Future<void> unsubscribeContact(String email) async {
    try {
      await _recipientsRepository.unsubscribe(email);
    } catch (e) {
      throw ContactException('Failed to unsubscribe contact: ${e.toString()}');
    }
  }

  // Import/Export functionality
  Future<ImportResult> importFromCsv(
    String filePath, {
    bool hasHeaders = true,
    Map<String, String>? columnMapping,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw ContactException('File not found: $filePath');
      }

      final csvContent = await file.readAsString();
      final List<List<dynamic>> csvData = const CsvToListConverter().convert(
        csvContent,
      );

      if (csvData.isEmpty) {
        throw ContactException('CSV file is empty');
      }

      List<String> headers = [];
      int dataStartIndex = 0;

      if (hasHeaders) {
        headers =
            csvData[0].map((e) => e.toString().toLowerCase().trim()).toList();
        dataStartIndex = 1;
      } else {
        // Generate default headers
        headers = List.generate(csvData[0].length, (index) => 'column_$index');
      }

      final contacts = <Recipient>[];
      final errors = <String>[];
      int successCount = 0;
      int errorCount = 0;

      for (int i = dataStartIndex; i < csvData.length; i++) {
        try {
          final row = csvData[i];
          final contact = _parseContactFromCsvRow(row, headers, columnMapping);

          if (contact != null) {
            contacts.add(contact);
            successCount++;
          } else {
            errors.add('Row ${i + 1}: Unable to parse contact data');
            errorCount++;
          }
        } catch (e) {
          errors.add('Row ${i + 1}: ${e.toString()}');
          errorCount++;
        }
      }

      // Import contacts in batches
      if (contacts.isNotEmpty) {
        await createContacts(contacts);
      }

      return ImportResult(
        totalRows: csvData.length - dataStartIndex,
        successCount: successCount,
        errorCount: errorCount,
        errors: errors,
        importedContacts: contacts,
      );
    } catch (e) {
      throw ContactException('Failed to import CSV: ${e.toString()}');
    }
  }

  Future<String> exportToCsv(List<Recipient> contacts, String filePath) async {
    try {
      final headers = [
        'Name',
        'Email',
        'Company',
        'Phone',
        'Tags',
        'Groups',
        'Subscribed',
        'Created At',
        'Custom Fields',
      ];

      final rows = <List<dynamic>>[headers];

      for (final contact in contacts) {
        rows.add([
          contact.name,
          contact.email,
          contact.company ?? '',
          contact.phone ?? '',
          contact.tags.join(';'),
          contact.groups.join(';'),
          contact.isSubscribed ? 'Yes' : 'No',
          contact.createdAt.toIso8601String(),
          jsonEncode(contact.customFields),
        ]);
      }

      final csvContent = const ListToCsvConverter().convert(rows);
      final file = File(filePath);
      await file.writeAsString(csvContent);

      return filePath;
    } catch (e) {
      throw ContactException('Failed to export CSV: ${e.toString()}');
    }
  }

  // Group Management
  Future<String> createGroup(ContactGroup group) async {
    try {
      return await _groupsRepository.create(group);
    } catch (e) {
      throw ContactException('Failed to create group: ${e.toString()}');
    }
  }

  Future<ContactGroup?> getGroup(String id) async {
    try {
      return await _groupsRepository.getById(id);
    } catch (e) {
      throw ContactException('Failed to get group: ${e.toString()}');
    }
  }

  Future<List<ContactGroup>> getGroups({
    int? limit,
    int? offset,
    String? searchQuery,
    GroupType? type,
    bool? isActive,
  }) async {
    try {
      return await _groupsRepository.getAll(
        limit: limit,
        offset: offset,
        searchQuery: searchQuery,
        type: type,
      );
    } catch (e) {
      throw ContactException('Failed to get groups: ${e.toString()}');
    }
  }

  Future<void> updateGroup(ContactGroup group) async {
    try {
      await _groupsRepository.update(group);
    } catch (e) {
      throw ContactException('Failed to update group: ${e.toString()}');
    }
  }

  Future<void> deleteGroup(String id) async {
    try {
      await _groupsRepository.delete(id);
    } catch (e) {
      throw ContactException('Failed to delete group: ${e.toString()}');
    }
  }

  Future<void> addContactsToGroup(
    String groupId,
    List<String> contactIds,
  ) async {
    try {
      await _groupsRepository.addContacts(groupId, contactIds);
    } catch (e) {
      throw ContactException(
        'Failed to add contacts to group: ${e.toString()}',
      );
    }
  }

  Future<void> removeContactsFromGroup(
    String groupId,
    List<String> contactIds,
  ) async {
    try {
      await _groupsRepository.removeContacts(groupId, contactIds);
    } catch (e) {
      throw ContactException(
        'Failed to remove contacts from group: ${e.toString()}',
      );
    }
  }

  // Tag Management
  Future<List<String>> getAllTags() async {
    try {
      return await _recipientsRepository.getAllTags();
    } catch (e) {
      throw ContactException('Failed to get tags: ${e.toString()}');
    }
  }

  Future<List<String>> getAllGroups() async {
    try {
      return await _recipientsRepository.getAllGroups();
    } catch (e) {
      throw ContactException('Failed to get groups: ${e.toString()}');
    }
  }

  Future<void> addTagsToContacts(
    List<String> contactIds,
    List<String> tags,
  ) async {
    try {
      for (final contactId in contactIds) {
        final contact = await getContact(contactId);
        if (contact != null) {
          final updatedTags = {...contact.tags, ...tags}.toList();
          await updateContact(contact.copyWith(tags: updatedTags));
        }
      }
    } catch (e) {
      throw ContactException('Failed to add tags to contacts: ${e.toString()}');
    }
  }

  Future<void> removeTagsFromContacts(
    List<String> contactIds,
    List<String> tags,
  ) async {
    try {
      for (final contactId in contactIds) {
        final contact = await getContact(contactId);
        if (contact != null) {
          final updatedTags =
              contact.tags.where((tag) => !tags.contains(tag)).toList();
          await updateContact(contact.copyWith(tags: updatedTags));
        }
      }
    } catch (e) {
      throw ContactException(
        'Failed to remove tags from contacts: ${e.toString()}',
      );
    }
  }

  // Segmentation
  Future<List<Recipient>> getContactsBySegment(ContactSegment segment) async {
    try {
      return await getContacts(
        searchQuery: segment.searchQuery,
        tags: segment.tags,
        groups: segment.groups,
        isSubscribed: segment.isSubscribed,
      );
    } catch (e) {
      throw ContactException(
        'Failed to get contacts by segment: ${e.toString()}',
      );
    }
  }

  // Statistics
  Future<ContactStats> getContactStats() async {
    try {
      final totalContacts = await getContactCount();
      final subscribedContacts = await getContactCount(isSubscribed: true);
      final unsubscribedContacts = await getContactCount(isSubscribed: false);
      final allTags = await getAllTags();
      final allGroups = await getAllGroups();

      return ContactStats(
        totalContacts: totalContacts,
        subscribedContacts: subscribedContacts,
        unsubscribedContacts: unsubscribedContacts,
        totalTags: allTags.length,
        totalGroups: allGroups.length,
      );
    } catch (e) {
      throw ContactException('Failed to get contact stats: ${e.toString()}');
    }
  }

  // Private helper methods
  Recipient? _parseContactFromCsvRow(
    List<dynamic> row,
    List<String> headers,
    Map<String, String>? columnMapping,
  ) {
    try {
      final data = <String, dynamic>{};

      for (int i = 0; i < headers.length && i < row.length; i++) {
        final header = headers[i];
        final value = row[i]?.toString().trim() ?? '';
        data[header] = value;
      }

      // Apply column mapping if provided
      final mappedData = <String, dynamic>{};
      if (columnMapping != null) {
        for (final entry in columnMapping.entries) {
          final csvColumn = entry.key.toLowerCase();
          final recipientField = entry.value;
          if (data.containsKey(csvColumn)) {
            mappedData[recipientField] = data[csvColumn];
          }
        }
      } else {
        mappedData.addAll(data);
      }

      // Extract required fields
      final name = mappedData['name'] ?? mappedData['full_name'] ?? '';
      final email = mappedData['email'] ?? '';

      if (name.isEmpty || email.isEmpty) {
        return null;
      }

      // Parse tags and groups
      final tags = _parseListField(mappedData['tags']);
      final groups = _parseListField(mappedData['groups']);

      // Parse custom fields
      final customFields = <String, dynamic>{};
      for (final entry in mappedData.entries) {
        if (![
          'name',
          'email',
          'company',
          'phone',
          'tags',
          'groups',
          'subscribed',
        ].contains(entry.key)) {
          customFields[entry.key] = entry.value;
        }
      }

      return Recipient(
        name: name,
        email: email,
        company: mappedData['company'],
        phone: mappedData['phone'],
        tags: tags,
        groups: groups,
        customFields: customFields,
        isSubscribed: _parseBoolField(mappedData['subscribed']) ?? true,
      );
    } catch (e) {
      return null;
    }
  }

  List<String> _parseListField(dynamic value) {
    if (value == null || value.toString().isEmpty) return [];
    return value
        .toString()
        .split(';')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();
  }

  bool? _parseBoolField(dynamic value) {
    if (value == null) return null;
    final str = value.toString().toLowerCase();
    if (['true', 'yes', '1', 'y'].contains(str)) return true;
    if (['false', 'no', '0', 'n'].contains(str)) return false;
    return null;
  }
}

class ImportResult {
  final int totalRows;
  final int successCount;
  final int errorCount;
  final List<String> errors;
  final List<Recipient> importedContacts;

  ImportResult({
    required this.totalRows,
    required this.successCount,
    required this.errorCount,
    required this.errors,
    required this.importedContacts,
  });

  bool get hasErrors => errorCount > 0;
  double get successRate =>
      totalRows > 0 ? (successCount / totalRows) * 100 : 0;
}

class ContactSegment {
  final String? searchQuery;
  final List<String>? tags;
  final List<String>? groups;
  final bool? isSubscribed;

  ContactSegment({this.searchQuery, this.tags, this.groups, this.isSubscribed});
}

class ContactStats {
  final int totalContacts;
  final int subscribedContacts;
  final int unsubscribedContacts;
  final int totalTags;
  final int totalGroups;

  ContactStats({
    required this.totalContacts,
    required this.subscribedContacts,
    required this.unsubscribedContacts,
    required this.totalTags,
    required this.totalGroups,
  });

  double get subscriptionRate =>
      totalContacts > 0 ? (subscribedContacts / totalContacts) * 100 : 0;
}

class ContactException implements Exception {
  final String message;
  ContactException(this.message);

  @override
  String toString() => 'ContactException: $message';
}
