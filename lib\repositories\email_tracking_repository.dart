// lib/repositories/email_tracking_repository.dart
import '../models/email_tracking.dart';
import '../services/database_service.dart';

class EmailTrackingRepository {
  final DatabaseService _databaseService = DatabaseService();

  Future<void> createTrackingRecord(EmailTracking tracking) async {
    final db = await _databaseService.database;
    await db.insert('email_tracking', tracking.toMap());
  }

  Future<EmailTracking?> getTrackingById(String id) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'email_tracking',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return EmailTracking.fromMap(maps.first);
    }
    return null;
  }

  Future<List<EmailTracking>> getTrackingByCampaign(String campaignId) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'email_tracking',
      where: 'campaign_id = ?',
      whereArgs: [campaignId],
      orderBy: 'sent_at DESC',
    );
    return maps.map((map) => EmailTracking.fromMap(map)).toList();
  }

  Future<List<EmailTracking>> getTrackingByContact(String contactId) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'email_tracking',
      where: 'contact_id = ?',
      whereArgs: [contactId],
      orderBy: 'sent_at DESC',
    );
    return maps.map((map) => EmailTracking.fromMap(map)).toList();
  }

  Future<void> updateTrackingRecord(EmailTracking tracking) async {
    final db = await _databaseService.database;
    await db.update(
      'email_tracking',
      tracking.toMap(),
      where: 'id = ?',
      whereArgs: [tracking.id],
    );
  }

  Future<void> recordEmailSent(
    String campaignId,
    String contactId,
    String messageId,
  ) async {
    final tracking = EmailTracking(
      id: messageId,
      campaignId: campaignId,
      contactId: contactId,
      status: EmailStatus.sent,
      sentAt: DateTime.now(),
    );
    await createTrackingRecord(tracking);
  }

  Future<void> recordEmailDelivered(String messageId) async {
    final tracking = await getTrackingById(messageId);
    if (tracking != null) {
      final updatedTracking = tracking.copyWith(
        status: EmailStatus.delivered,
        deliveredAt: DateTime.now(),
      );
      await updateTrackingRecord(updatedTracking);
    }
  }

  Future<void> recordEmailOpened(String messageId) async {
    final tracking = await getTrackingById(messageId);
    if (tracking != null) {
      final updatedTracking = tracking.copyWith(
        status: EmailStatus.opened,
        openedAt: DateTime.now(),
        openCount: tracking.openCount + 1,
      );
      await updateTrackingRecord(updatedTracking);
    }
  }

  Future<void> recordEmailClicked(String messageId, String clickedUrl) async {
    final tracking = await getTrackingById(messageId);
    if (tracking != null) {
      final updatedTracking = tracking.copyWith(
        status: EmailStatus.clicked,
        clickedAt: DateTime.now(),
        clickCount: tracking.clickCount + 1,
        clickedUrls: [...tracking.clickedUrls, clickedUrl],
      );
      await updateTrackingRecord(updatedTracking);
    }
  }

  Future<void> recordEmailBounced(String messageId, String bounceReason) async {
    final tracking = await getTrackingById(messageId);
    if (tracking != null) {
      final updatedTracking = tracking.copyWith(
        status: EmailStatus.bounced,
        bouncedAt: DateTime.now(),
        bounceReason: bounceReason,
      );
      await updateTrackingRecord(updatedTracking);
    }
  }

  Future<void> recordEmailUnsubscribed(String messageId) async {
    final tracking = await getTrackingById(messageId);
    if (tracking != null) {
      final updatedTracking = tracking.copyWith(
        status: EmailStatus.unsubscribed,
        unsubscribedAt: DateTime.now(),
      );
      await updateTrackingRecord(updatedTracking);
    }
  }

  Future<Map<String, int>> getCampaignStats(String campaignId) async {
    final db = await _databaseService.database;

    final sentResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE campaign_id = ?',
      [campaignId],
    );

    final deliveredResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE campaign_id = ? AND status IN (?, ?, ?, ?)',
      [
        campaignId,
        EmailStatus.delivered.toString(),
        EmailStatus.opened.toString(),
        EmailStatus.clicked.toString(),
        EmailStatus.unsubscribed.toString(),
      ],
    );

    final openedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE campaign_id = ? AND status IN (?, ?)',
      [
        campaignId,
        EmailStatus.opened.toString(),
        EmailStatus.clicked.toString(),
      ],
    );

    final clickedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE campaign_id = ? AND status = ?',
      [campaignId, EmailStatus.clicked.toString()],
    );

    final bouncedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE campaign_id = ? AND status = ?',
      [campaignId, EmailStatus.bounced.toString()],
    );

    final unsubscribedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE campaign_id = ? AND status = ?',
      [campaignId, EmailStatus.unsubscribed.toString()],
    );

    return {
      'sent': sentResult.first['count'] as int,
      'delivered': deliveredResult.first['count'] as int,
      'opened': openedResult.first['count'] as int,
      'clicked': clickedResult.first['count'] as int,
      'bounced': bouncedResult.first['count'] as int,
      'unsubscribed': unsubscribedResult.first['count'] as int,
    };
  }

  Future<List<EmailTracking>> getRecentActivity(int limit) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'email_tracking',
      orderBy: 'sent_at DESC',
      limit: limit,
    );
    return maps.map((map) => EmailTracking.fromMap(map)).toList();
  }

  Future<double> getCampaignOpenRate(String campaignId) async {
    final stats = await getCampaignStats(campaignId);
    final sent = stats['sent'] ?? 0;
    final opened = stats['opened'] ?? 0;

    if (sent == 0) return 0.0;
    return (opened / sent) * 100;
  }

  Future<double> getCampaignClickRate(String campaignId) async {
    final stats = await getCampaignStats(campaignId);
    final sent = stats['sent'] ?? 0;
    final clicked = stats['clicked'] ?? 0;

    if (sent == 0) return 0.0;
    return (clicked / sent) * 100;
  }

  Future<double> getCampaignBounceRate(String campaignId) async {
    final stats = await getCampaignStats(campaignId);
    final sent = stats['sent'] ?? 0;
    final bounced = stats['bounced'] ?? 0;

    if (sent == 0) return 0.0;
    return (bounced / sent) * 100;
  }

  Future<List<EmailTracking>> getFailedEmails(String campaignId) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'email_tracking',
      where: 'campaign_id = ? AND status = ?',
      whereArgs: [campaignId, EmailStatus.bounced.toString()],
      orderBy: 'sent_at DESC',
    );
    return maps.map((map) => EmailTracking.fromMap(map)).toList();
  }

  Future<void> deleteTrackingRecords(String campaignId) async {
    final db = await _databaseService.database;
    await db.delete(
      'email_tracking',
      where: 'campaign_id = ?',
      whereArgs: [campaignId],
    );
  }

  Future<Map<String, dynamic>> getOverallStats() async {
    final db = await _databaseService.database;

    final totalSentResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking',
    );

    final totalDeliveredResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE status IN (?, ?, ?, ?)',
      [
        EmailStatus.delivered.toString(),
        EmailStatus.opened.toString(),
        EmailStatus.clicked.toString(),
        EmailStatus.unsubscribed.toString(),
      ],
    );

    final totalOpenedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE status IN (?, ?)',
      [EmailStatus.opened.toString(), EmailStatus.clicked.toString()],
    );

    final totalClickedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM email_tracking WHERE status = ?',
      [EmailStatus.clicked.toString()],
    );

    final totalSent = totalSentResult.first['count'] as int;
    final totalDelivered = totalDeliveredResult.first['count'] as int;
    final totalOpened = totalOpenedResult.first['count'] as int;
    final totalClicked = totalClickedResult.first['count'] as int;

    return {
      'totalSent': totalSent,
      'totalDelivered': totalDelivered,
      'totalOpened': totalOpened,
      'totalClicked': totalClicked,
      'deliveryRate': totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0.0,
      'openRate': totalSent > 0 ? (totalOpened / totalSent) * 100 : 0.0,
      'clickRate': totalSent > 0 ? (totalClicked / totalSent) * 100 : 0.0,
    };
  }

  // Get contact engagement data
  Future<Map<String, dynamic>> getContactEngagement(String contactId) async {
    final trackingRecords = await getTrackingByContact(contactId);

    int totalEmails = trackingRecords.length;
    int openedEmails = trackingRecords.where((t) => t.isOpened).length;
    int clickedEmails = trackingRecords.where((t) => t.isClicked).length;
    int bouncedEmails = trackingRecords.where((t) => t.isBounced).length;

    double openRate =
        totalEmails > 0 ? (openedEmails / totalEmails) * 100 : 0.0;
    double clickRate =
        totalEmails > 0 ? (clickedEmails / totalEmails) * 100 : 0.0;
    double bounceRate =
        totalEmails > 0 ? (bouncedEmails / totalEmails) * 100 : 0.0;

    // Calculate engagement score (0-100)
    double engagementScore = 0.0;
    if (totalEmails > 0) {
      engagementScore = (openRate * 0.4) + (clickRate * 0.6);
    }

    return {
      'totalEmails': totalEmails,
      'openedEmails': openedEmails,
      'clickedEmails': clickedEmails,
      'bouncedEmails': bouncedEmails,
      'openRate': openRate,
      'clickRate': clickRate,
      'bounceRate': bounceRate,
      'engagementScore': engagementScore,
      'lastActivity':
          trackingRecords.isNotEmpty
              ? trackingRecords.first.sentAt.toIso8601String()
              : null,
    };
  }
}
